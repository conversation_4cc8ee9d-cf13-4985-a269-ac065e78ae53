import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { gameService } from '@/services/game.service';
import { toast } from 'sonner';
import { Game } from '@/types/game.types';
import { authUtils } from '@/utils/auth.utils';

/**
 * Get user's favorite games (full game objects)
 * Following favorites.md specification
 * Returns empty favorites for unauthenticated users
 */
export const useFavorites = () => {
  const isAuthenticated = authUtils.getUserData() !== null;

  return useQuery({
    queryKey: ['games', 'favorites'],
    queryFn: async () => {
      if (!isAuthenticated) {
        // Return empty favorites for unauthenticated users
        return {
          status: 'success',
          message: 'No favorites (not authenticated)',
          data: {
            favorites: [],
            count: 0
          }
        };
      }
      return gameService.getFavorites();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on 401 errors for unauthenticated users
      if (error?.response?.status === 401) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

/**
 * Add game to favorites
 * Implements optimistic updates and error handling as per favorites.md
 * Shows login prompt for unauthenticated users
 */
export const useAddToFavorites = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (gameId: string) => {
      const isAuthenticated = authUtils.getUserData() !== null;
      if (!isAuthenticated) {
        toast.info('Please log in to add games to your favorites');
        throw new Error('Authentication required');
      }
      return gameService.addToFavorites(gameId);
    },
    onMutate: async (gameId) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['games', 'favorites'] });

      // Snapshot previous value
      const previousFavorites = queryClient.getQueryData(['games', 'favorites']);

      // Note: For full optimistic update, we'd need the game object
      // For now, we'll just invalidate after success to refetch the data

      return { previousFavorites };
    },
    onError: (err: any, gameId, context) => {
      // Rollback on error
      queryClient.setQueryData(['games', 'favorites'], context?.previousFavorites);

      // Handle specific error messages from API as per favorites.md
      if (err.response?.status === 409) {
        toast.error('Game is already in favorites');
      } else if (err.response?.status === 404) {
        toast.error('Game not found');
      } else if (err.response?.status === 400) {
        toast.error('Game ID is required');
      } else if (err.response?.status === 401) {
        toast.error('Authentication required');
      } else if (err.response?.status === 403) {
        toast.error('Insufficient permissions. Player role required.');
      } else {
        toast.error('Failed to add game to favorites');
      }
    },
    onSuccess: (data) => {
      toast.success(data.message);
    },
    onSettled: () => {
      // Refetch to ensure consistency
      queryClient.invalidateQueries({ queryKey: ['games', 'favorites'] });
    },
  });
};

/**
 * Remove game from favorites
 * Implements optimistic updates and error handling as per favorites.md
 * Shows login prompt for unauthenticated users
 */
export const useRemoveFromFavorites = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (gameId: string) => {
      const isAuthenticated = authUtils.getUserData() !== null;
      if (!isAuthenticated) {
        toast.info('Please log in to manage your favorites');
        throw new Error('Authentication required');
      }
      return gameService.removeFromFavorites(gameId);
    },
    onMutate: async (gameId) => {
      await queryClient.cancelQueries({ queryKey: ['games', 'favorites'] });
      const previousFavorites = queryClient.getQueryData(['games', 'favorites']);

      // Optimistically remove from favorites
      queryClient.setQueryData(['games', 'favorites'], (old: any) => {
        if (!old?.data?.favorites) return old;

        return {
          ...old,
          data: {
            favorites: old.data.favorites.filter((game: Game) => game.id !== gameId),
            count: old.data.count - 1
          }
        };
      });

      return { previousFavorites };
    },
    onError: (err: any, gameId, context) => {
      queryClient.setQueryData(['games', 'favorites'], context?.previousFavorites);

      // Handle specific error messages from API as per favorites.md
      if (err.response?.status === 404) {
        toast.error('Game is not in favorites');
      } else if (err.response?.status === 400) {
        toast.error('Game ID is required');
      } else if (err.response?.status === 401) {
        toast.error('Authentication required');
      } else if (err.response?.status === 403) {
        toast.error('Insufficient permissions. Player role required.');
      } else {
        toast.error('Failed to remove game from favorites');
      }
    },
    onSuccess: (data) => {
      toast.success(data.message);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['games', 'favorites'] });
    },
  });
};

/**
 * Helper hook to check if a game is favorited
 * Uses the favorites data to determine favorite status
 * Returns false for unauthenticated users
 */
export const useIsFavorite = (gameId: string) => {
  const { data: favoritesData } = useFavorites();
  const isAuthenticated = authUtils.getUserData() !== null;

  if (!isAuthenticated) {
    return false;
  }

  return favoritesData?.data?.favorites?.some(game => game.id === gameId) || false;
};

/**
 * Combined hook for toggling favorites
 * Automatically determines whether to add or remove based on current status
 */
export const useToggleFavorite = () => {
  const addToFavorites = useAddToFavorites();
  const removeFromFavorites = useRemoveFromFavorites();

  return {
    toggleFavorite: (gameId: string, isFavorite: boolean) => {
      if (isFavorite) {
        removeFromFavorites.mutate(gameId);
      } else {
        addToFavorites.mutate(gameId);
      }
    },
    isLoading: addToFavorites.isPending || removeFromFavorites.isPending,
  };
};

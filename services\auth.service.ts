import axios from 'axios';
import { authUtils } from '@/utils/auth.utils';
import { toast } from 'sonner';
import { API_BASE_URL } from '@/config';

const API_URL = `${API_BASE_URL}/api/users`;

// Create axios instance with interceptors
const apiClient = axios.create({
  baseURL: API_URL,
  withCredentials: true, // Important for cookies
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  timeout: 10000
});

// Add request interceptor to include CSRF token
apiClient.interceptors.request.use(
  async config => {
    // Skip for GET requests
    if (config.method?.toLowerCase() === 'get') {
      return config;
    }

    // For non-GET requests, ensure we have a valid token
    // Skip for login and refresh-token endpoints
    if (!config.url?.includes('refresh-token') && !config.url?.includes('login')) {
      await authUtils.ensureValidToken();
    }

    // Add CSRF token to headers for non-GET requests
    const csrfToken = authUtils.getCsrfToken();
    if (csrfToken) {
      config.headers['X-CSRF-Token'] = csrfToken;
    }

    return config;
  },
  error => Promise.reject(error)
);

// Add response interceptor
apiClient.interceptors.response.use(
  response => response,
  async error => {
    // Check if we're in a browser environment
    const isBrowser = typeof window !== 'undefined';

    // Check if this is a network error (server down, connection refused, etc.)
    if (!error.response && error.request && isBrowser) {
      // Clear auth data
      authUtils.clearAuthData();

      // Only redirect if we're not already on a public page
      const isPublicPage = window.location.pathname === '/' ||
                          window.location.pathname === '/en' ||
                          window.location.pathname === '/fr' ||
                          window.location.pathname === '/casino' ||
                          window.location.pathname === '/sport' ||
                          window.location.pathname === '/live';

      if (!isPublicPage) {
        toast.error('Connection to server failed. Please try again later.');
        window.location.href = '/';
      }

      return Promise.reject(error);
    }

    // Skip 401 handling for login endpoint
    if (error.config?.url === '/login' && error.response?.status === 401) {
      return Promise.reject(error);
    }

    // Handle 401 errors (unauthorized)
    if (error.response?.status === 401) {
      // Try to refresh the token if not already trying to refresh
      if (!error.config?.url?.includes('refresh-token')) {
        try {
          await authService.refreshToken();

          // Retry the original request
          const newConfig = { ...error.config };

          // Update CSRF token for the retry
          if (newConfig.method?.toLowerCase() !== 'get') {
            const csrfToken = authUtils.getCsrfToken();
            if (csrfToken) {
              newConfig.headers['X-CSRF-Token'] = csrfToken;
            }
          }

          return axios(newConfig);
        } catch (refreshError) {
          // Clear auth data but don't redirect if we're already on the login page
          authUtils.clearAuthData();

          // Only redirect if we're not already on a public page
          const isPublicPage = isBrowser && (
            window.location.pathname === '/' ||
            window.location.pathname === '/en' ||
            window.location.pathname === '/fr' ||
            window.location.pathname === '/casino' ||
            window.location.pathname === '/sport' ||
            window.location.pathname === '/live'
          );

          if (isBrowser && !isPublicPage) {
            toast.error('Session expired. Please login again.');
            window.location.href = '/';
          }

          return Promise.reject(refreshError);
        }
      }

      // If we're already trying to refresh, just clear auth and redirect if needed
      authUtils.clearAuthData();

      // Only redirect if we're not already on a public page
      const isPublicPage = isBrowser && (
        window.location.pathname === '/' ||
        window.location.pathname === '/en' ||
        window.location.pathname === '/fr' ||
        window.location.pathname === '/casino' ||
        window.location.pathname === '/sport' ||
        window.location.pathname === '/live'
      );

      if (isBrowser && !isPublicPage) {
        toast.error('Session expired. Please login again.');
        window.location.href = '/';
      }
    }

    // Handle other error status codes
    if (error.response?.status >= 500 && isBrowser) {
      const isPublicPage = window.location.pathname === '/' ||
                          window.location.pathname === '/en' ||
                          window.location.pathname === '/fr' ||
                          window.location.pathname === '/casino' ||
                          window.location.pathname === '/sport' ||
                          window.location.pathname === '/live';

      if (!isPublicPage) {
        toast.error('Server error. Please try again later.');
      }
    }

    return Promise.reject(error);
  }
);

export interface LoginResponse {
  status: string;
  data: {
    user: {
      id: number;
      username: string;
      role: string;
      balance: number;
      currency: string;
    };
    csrfToken: string;
  };
}

export interface RefreshTokenResponse {
  status: string;
  data: {
    csrfToken: string;
  };
}

export interface UserDetailsResponse {
  status: string;
  data: {
    id: number;
    username: string;
    role: string;
    balance: number;
    currency: string;
  };
}

export const authService = {
  async login(username: string, password: string): Promise<LoginResponse> {
    try {
      // Use axios directly for login to bypass the interceptors
      const response = await axios.post(`${API_URL}/login`, {
        username,
        password
      }, {
        withCredentials: true // Important for cookies
      });

      // Store user data
      if (response.data?.data?.user) {
        authUtils.setUserData(response.data.data.user);
      }

      // Check if we have a CSRF token in the response
      if (response.data?.data?.csrfToken) {
        // Store the token in cookie
        authUtils.storeCsrfToken(response.data.data.csrfToken);
      }

      return response.data;
    } catch (error) {
      throw error;
    }
  },

  async refreshToken(): Promise<RefreshTokenResponse> {
    try {
      const response = await axios.post(`${API_URL}/refresh-token`, {}, {
        withCredentials: true // Important for cookies
      });

      // Check if we have a CSRF token in the response
      if (response.data?.data?.csrfToken) {
        // Store the token in cookie
        authUtils.storeCsrfToken(response.data.data.csrfToken);
      }

      return response.data;
    } catch (error: any) {
      // Clear auth data
      authUtils.clearAuthData();

      const isBrowser = typeof window !== 'undefined';

      // Only redirect if we're not already on a public page
      const isPublicPage = isBrowser && (
        window.location.pathname === '/' ||
        window.location.pathname === '/en' ||
        window.location.pathname === '/fr' ||
        window.location.pathname === '/casino' ||
        window.location.pathname === '/sport' ||
        window.location.pathname === '/live'
      );

      if (isBrowser && !isPublicPage) {
        // Check if this is a network error (server down, connection refused, etc.)
        if (!error.response && error.request) {
          toast.error('Connection to server failed. Please try again later.');
        } else {
          toast.error('Session expired. Please login again.');
        }
        window.location.href = '/';
      }

      throw error;
    }
  },

  async logout(): Promise<void> {
    try {
      await apiClient.post('/logout');
      authUtils.clearAuthData();
      window.location.href = '/';
    } catch (error) {
      // Clear auth data anyway
      authUtils.clearAuthData();
      window.location.href = '/';
    }
  },

  async getUserDetails(): Promise<UserDetailsResponse> {
    try {
      const response = await apiClient.get('/me');
      return response.data;
    } catch (error: any) {
      // If the error is not a 401 (which is handled by the interceptor)
      // we still want to show a generic error message
      if (error?.response?.status !== 401 && typeof window !== 'undefined') {
        toast.error('Failed to get user details. Please try again.');
      }
      throw error;
    }
  }
};

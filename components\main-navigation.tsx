"use client"

import { useState } from "react"
import Link from "next/link"
import { ChevronDown, Clock } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from "@/components/ui/dropdown-menu"

interface MainNavigationProps {
  currentDate?: string
}

export default function MainNavigation({ currentDate }: MainNavigationProps) {
  const [language, setLanguage] = useState("English")

  const navItems = [
    { name: "Sport", href: "/sport" },
    { name: "Live", href: "/live" },
    { name: "Casino", href: "/casino" },
    { name: "Livecasino", href: "/livecasino" },
    { name: "Virtuals", href: "/virtuals" },
    { name: "Evolution", href: "/evolution" },
    { name: "Roulettes", href: "/roulettes" },
    { name: "Blackjack", href: "/blackjack" },
    { name: "Spin2W<PERSON>", href: "/spin2win" },
    { name: "Horse Racing", href: "/horse-racing" },
    { name: "Lotto", href: "/lotto" },
    { name: "Aviator", href: "/aviator" },
  ]

  return (
    <nav className="bg-yellow-500 text-black">
      <div className="flex justify-between">
        <div className="flex overflow-x-auto hide-scrollbar">
          {navItems.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className="px-3 py-2 text-sm whitespace-nowrap hover:bg-yellow-400 transition-colors"
            >
              {item.name}
            </Link>
          ))}
        </div>

        <div className="flex items-center">
          <DropdownMenu>
            <DropdownMenuTrigger className="px-3 py-2 text-sm flex items-center hover:bg-yellow-400 transition-colors focus:outline-none">
              {language} <ChevronDown className="ml-1 h-3 w-3" />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuRadioGroup value={language} onValueChange={setLanguage}>
                <DropdownMenuRadioItem value="English">English</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="Français">Français</DropdownMenuRadioItem>
              </DropdownMenuRadioGroup>
            </DropdownMenuContent>
          </DropdownMenu>

          {currentDate && (
            <div className="flex items-center px-3 py-2">
              <Clock className="h-3 w-3 mr-1" />
              <span className="text-xs">{currentDate}</span>
            </div>
          )}
        </div>
      </div>
    </nav>
  )
}

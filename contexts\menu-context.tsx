"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

interface MenuContextType {
  isMobileMenuOpen: boolean
  isUserSidebarOpen: boolean
  openMobileMenu: () => void
  closeMobileMenu: () => void
  openUserSidebar: () => void
  closeUserSidebar: () => void
}

const MenuContext = createContext<MenuContextType | undefined>(undefined)

export function MenuProvider({ children }: { children: ReactNode }) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isUserSidebarOpen, setIsUserSidebarOpen] = useState(false)

  // Function to toggle body scroll
  const toggleBodyScroll = (disable: boolean) => {
    if (typeof document !== 'undefined') {
      if (disable) {
        document.body.style.overflow = 'hidden'
      } else {
        document.body.style.overflow = ''
      }
    }
  }

  // Effect to handle body scroll based on sidebar/menu state
  useEffect(() => {
    toggleBodyScroll(isMobileMenuOpen || isUserSidebarOpen)

    // Cleanup function to ensure scroll is re-enabled when component unmounts
    return () => {
      toggleBodyScroll(false)
    }
  }, [isMobileMenuOpen, isUserSidebarOpen])

  const openMobileMenu = () => {
    setIsMobileMenuOpen(true)
    // Close user sidebar if it's open
    if (isUserSidebarOpen) {
      setIsUserSidebarOpen(false)
    }
  }

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false)
  }

  const openUserSidebar = () => {
    setIsUserSidebarOpen(true)
    // Close mobile menu if it's open
    if (isMobileMenuOpen) {
      setIsMobileMenuOpen(false)
    }
  }

  const closeUserSidebar = () => {
    setIsUserSidebarOpen(false)
  }

  return (
    <MenuContext.Provider
      value={{
        isMobileMenuOpen,
        isUserSidebarOpen,
        openMobileMenu,
        closeMobileMenu,
        openUserSidebar,
        closeUserSidebar,
      }}
    >
      {children}
    </MenuContext.Provider>
  )
}

export function useMenuContext() {
  const context = useContext(MenuContext)
  if (context === undefined) {
    throw new Error('useMenuContext must be used within a MenuProvider')
  }
  return context
}

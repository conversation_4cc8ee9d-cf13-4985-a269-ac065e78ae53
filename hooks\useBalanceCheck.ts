"use client"

import { useState, useEffect } from 'react';
import { userService } from '@/services/user.service';
import { authUtils, User } from '@/utils/auth.utils';

// Balance check interval in milliseconds (1 minute)
const BALANCE_CHECK_INTERVAL = 60 * 1000;

export function useBalanceCheck() {
  const [userData, setUserData] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Function to fetch user profile and update balance
  const fetchUserProfile = async () => {
    // Only proceed if the user is logged in
    const currentUserData = authUtils.getUserData();
    if (!currentUserData) return;

    setIsLoading(true);
    setError(null);

    try {
      // Call the API to get the latest user profile
      const response = await userService.getProfile();
      
      // The userService already updates the localStorage data
      // Now we just need to update our state
      const updatedUserData = authUtils.getUserData();
      setUserData(updatedUserData);
    } catch (err) {
      // Don't set error for 401 errors (they're handled by the interceptor)
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An unknown error occurred');
      }
      console.error('Error fetching user profile:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch and setup interval
  useEffect(() => {
    // Get initial user data from localStorage
    const initialUserData = authUtils.getUserData();
    setUserData(initialUserData);

    // Only set up the interval if the user is logged in
    if (initialUserData) {
      // Fetch immediately on mount
      fetchUserProfile();

      // Set up interval for periodic checks
      const intervalId = setInterval(fetchUserProfile, BALANCE_CHECK_INTERVAL);

      // Clean up interval on unmount
      return () => clearInterval(intervalId);
    }
  }, []);

  return { userData, isLoading, error, refetch: fetchUserProfile };
}

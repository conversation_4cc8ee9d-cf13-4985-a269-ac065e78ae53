import { apiClient } from '@/services/api.service';
import { toast } from 'sonner';

export interface TransactionHistoryParams {
  page?: number;
  limit?: number;
  startDate?: string;
  endDate?: string;
  type?: string;
}

export interface User {
  id: number;
  username: string;
  role: string;
}

export interface Transaction {
  id: number;
  fromUserId: number;
  toUserId: number;
  amount: number | string;
  type: string;
  createdAt: string;
  updatedAt: string;
  fromUser: User;
  toUser: User;
}

export interface TransactionHistoryResponse {
  status: string;
  data: Transaction[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export const cashflowService = {
  async getTransactionHistory(params: TransactionHistoryParams = {}): Promise<TransactionHistoryResponse> {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();

      if (params.page) {
        queryParams.append('page', params.page.toString());
      }

      if (params.limit) {
        queryParams.append('limit', params.limit.toString());
      }

      if (params.startDate) {
        queryParams.append('startDate', params.startDate);
      }

      if (params.endDate) {
        queryParams.append('endDate', params.endDate);
      }

      if (params.type && params.type !== 'all') {
        queryParams.append('type', params.type);
      }

      const queryString = queryParams.toString();
      const url = `/v1/cashflow/history/all${queryString ? `?${queryString}` : ''}`;

      const response = await apiClient.get(url);
      return response.data;
    } catch (error: any) {
      // Log the error
      console.error('Failed to get transaction history:', error);

      // Show a toast message for non-401 errors (401 is handled by the interceptor)
      if (error?.response?.status !== 401 && typeof window !== 'undefined') {
        toast.error('Failed to load transaction history. Please try again.');
      }

      throw error;
    }
  }
};

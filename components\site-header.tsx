"use client"

import { useState, useEffect, useRef } from "react"
import Link from "next/link"
import Image from "next/image"
import Logo from '@/components/logo';
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import MainNavigation from "@/components/main-navigation"
import MobileMenu from "@/components/mobile-menu"
import UserSidebar from "@/components/user-sidebar"
import mobileNavIcons from "./mobile-nav-icons"
import { navItems } from "@/data/nav-items"
import BannerGallery from "@/components/banner-gallery"
import { LoginDialog } from "@/components/login-dialog"
import { authUtils } from "@/utils/auth.utils"
import { authService } from "@/services/auth.service"
import { useBalanceCheck } from "@/hooks/useBalanceCheck"

export default function SiteHeader() {
  const pathname = usePathname()
  const [currentDate, setCurrentDate] = useState("")
  const [scrollIndicatorWidth, setScrollIndicatorWidth] = useState(0)
  const [scrollIndicatorOffset, setScrollIndicatorOffset] = useState(0)
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const navRef = useRef<HTMLDivElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Use the balance check hook to get and periodically update user data
  const { userData } = useBalanceCheck()

  // Check if user is logged in
  useEffect(() => {
    setIsLoggedIn(!!userData)
  }, [userData])

  // Update the date every second
  useEffect(() => {
    const updateDate = () => {
      const date = new Date()
      const formattedDate = `${date.getDate().toString().padStart(2, "0")}/${(date.getMonth() + 1).toString().padStart(2, "0")}/${date.getFullYear()} ${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}:${date.getSeconds().toString().padStart(2, "0")}`
      setCurrentDate(formattedDate)
    }

    updateDate()
    const interval = setInterval(updateDate, 1000)
    return () => clearInterval(interval)
  }, [])

  // Update scroll indicator
  useEffect(() => {
    if (pathname !== '/' || !navRef.current) return

    const updateScrollIndicator = () => {
      const nav = navRef.current
      if (!nav) return

      const scrollWidth = nav.scrollWidth
      const clientWidth = nav.clientWidth
      const scrollLeft = nav.scrollLeft

      // Calculate width of the indicator (clientWidth / scrollWidth) as a percentage
      const width = (clientWidth / scrollWidth) * 100
      setScrollIndicatorWidth(width)

      // Calculate position of the indicator
      const maxScrollLeft = scrollWidth - clientWidth
      const scrollPercentage = maxScrollLeft > 0 ? scrollLeft / maxScrollLeft : 0
      const offset = scrollPercentage * (100 - width)
      setScrollIndicatorOffset(offset)
    }

    // Initial update
    updateScrollIndicator()

    // Add scroll event listener
    const nav = navRef.current
    nav.addEventListener('scroll', updateScrollIndicator)

    // Clean up
    return () => {
      if (nav) {
        nav.removeEventListener('scroll', updateScrollIndicator)
      }
    }
  }, [pathname])



  // Navigation items are imported from @/data/nav-items

  // Handle dropdown toggle
  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle logout
  const handleLogout = async () => {
    try {
      await authService.logout();
      // Redirect is handled in the logout method
    } catch (error) {
      // Fallback redirect
      authUtils.clearAuthData();
      window.location.href = '/';
    }
  };

  // Get page title based on current route
  const getPageTitle = () => {
    switch (pathname) {
      case '/':
        return 'Home'
      case '/sport':
        return 'Sport'
      case '/live':
        return 'Live'
      case '/casino':
        return 'Casino'
      case '/livecasino':
        return 'Live Casino'
      case '/virtuals':
        return 'Virtuals'
      case '/evolution':
        return 'Evolution'
      case '/roulettes':
        return 'Roulettes'
      case '/blackjack':
        return 'Blackjack'
      case '/spin2win':
        return 'Spin2Win'
      case '/horse-racing':
        return 'Horse Racing'
      case '/lotto':
        return 'Lotto'
      case '/aviator':
        return 'Aviator'
      case '/terms':
        return 'Terms and Conditions'
      case '/affiliate':
        return 'Affiliate'
      case '/login':
        return 'Login'
      case '/profile/transactions':
        return 'Transactions'
      case '/profile/personal-details':
        return 'Personal Details'
      case '/profile/change-password':
        return 'Change Password'
      case '/profile/betting-history':
        return 'Change Password'
      default:
        return 'AfricaX1'
    }
  }

  return (
    <>
      {/* Header for mobile - sticky at the top */}
      <header className="flex justify-between items-center py-2 px-4 md:hidden fixed top-0 left-0 right-0 z-50 bg-[#121212] border-b border-gray-800">
  <MobileMenu navItems={navItems} />

  <Link href="/" className="flex items-center">
      <Logo className="w-28 h-12" />
  </Link>

  <UserSidebar />
</header>

      {/* Add padding for fixed header on mobile */}
      <div className="h-[60px] md:hidden"></div>

      {/* Mobile Connect Button - Only shown when not logged in */}
      {!isLoggedIn && (
        <div className="px-4 py-2 md:hidden">
          <Button className="w-full bg-yellow-500 text-black hover:bg-yellow-400 font-bold text-sm" asChild>
            <Link href="/login">LOGIN</Link>
          </Button>
        </div>
      )}

      {/* Conditional rendering based on route */}
      {pathname === '/' ? (
        /* Mobile navigation icons - Only shown on home page */
        <div className="relative flex flex-col md:hidden border-t border-gray-800">
          <div ref={navRef} className="swipable-nav flex overflow-x-auto w-full">
            {mobileNavIcons.map((item) => (
              <Link key={item.name} href={item.href} className="menu-item py-1 px-2 text-center flex-none" style={{ width: '80px' }}>
                <div className="icon-wrapper w-6 h-6 flex items-center justify-center">
                  <Image src={item.iconSrc} alt={item.name} width={24} height={24} className="text-white w-5 h-5" />
                </div>
                <span className="text-xs">{item.name}</span>
              </Link>
            ))}
          </div>

          {/* Scroll indicator bar */}
          <div className="scroll-indicator-container mx-4 border-b border-gray-800">
            <div
              className="scroll-indicator-bar"
              style={{
                width: `${scrollIndicatorWidth}%`,
                transform: `translateX(${scrollIndicatorOffset}%)`
              }}
            ></div>
          </div>
        </div>
      ) : (
        /* Mobile page title with home icon - Shown on all other pages */
        <div className="flex justify-between items-center px-4 py-2 md:hidden border-t border-b border-gray-800">
          <h1 className="text-white text-base font-bold">{getPageTitle()}</h1>

          <Link href="/" className="flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#FFE628" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-6 h-6">
              <path d="M19 12H5"></path>
              <path d="M12 19l-7-7 7-7"></path>
            </svg>
          </Link>
        </div>
      )}

      {/* Desktop Header - Hidden on mobile */}
      <header className="hidden md:flex justify-between items-center p-4 border-b border-gray-800">
        <div className="flex items-center">
          <Link href="/" className="flex items-center">
            <div className="w-28 h-auto">
              <Logo className="w-28 h-auto" />
            </div>
          </Link>
        </div>

        <div className="flex items-center gap-4">
          {isLoggedIn && userData ? (
            <>
              <div className="dropdown relative inline-block" ref={dropdownRef}>
                <div
                  className="bg-[#252525] rounded-full py-1 px-1 flex items-center justify-start gap-3 min-w-[275px] cursor-pointer dropdown-button"
                  onClick={toggleDropdown}
                >
                  <div className="bg-[#1a1a1a] rounded-full p-2.5 flex items-center justify-center">
                    <Image src="/icons/avatar-icon.svg" alt="User" width={20} height={20} className="text-white" style={{ filter: 'brightness(0) invert(1)' }} />
                  </div>
                  <div className="flex flex-col text-sm">
                    <span className="text-white-400 text-xs">{userData.username}</span>

                    <span className="text-gray-300">
                      <span className="font-bold">ID : </span>
                      <span className="text-white-400 text-xs"> {userData.id}</span>
                    </span>
                  </div>
                </div>

                {/* Dropdown content */}
                <div className={`dropdown-content absolute left-0 mt-2 w-[275px] bg-[#0f0f0f] rounded-md shadow-lg z-50 ${isDropdownOpen ? 'block' : 'hidden'}`}>
                  {/* Player Activities */}
                  <div className="menu-item">
                    <div className="px-4 py-2 text-sm text-white hover:bg-[#252525] flex justify-between items-center cursor-default w-full">
                      <span className="pl-0">Player Activities</span>
                      <Image
                        src="/icons/right-arrow-icon.svg"
                        alt="Arrow"
                        width={16}
                        height={16}
                        className=""
                        style={{ filter: 'brightness(0) invert(1)' }}
                      />
                    </div>
                    {/* Activities submenu */}
                    <div className="submenu">
                      <Link href="/profile/betting-history" className="block px-4 text-sm text-gray-300 hover:bg-[#252525] w-full">
                        <span className="pt-0">Bet History</span>
                      </Link>
                      <Link href="/profile/transactions" className="block px-4 text-sm text-gray-300 hover:bg-[#252525] w-full">
                        <span className="pt-0">Transactions</span>
                      </Link>
                    </div>
                  </div>

                  {/* Account Settings */}
                  <div className="menu-item">
                    <div className="px-4 py-2 text-sm text-white hover:bg-[#252525] flex justify-between items-center cursor-default w-full">
                      <span className="pl-0">Account Settings</span>
                      <Image
                        src="/icons/right-arrow-icon.svg"
                        alt="Arrow"
                        width={16}
                        height={16}
                        className=""
                        style={{ filter: 'brightness(0) invert(1)' }}
                      />
                    </div>
                    {/* Settings submenu */}
                    <div className="submenu">
                      <Link href="/profile/personal-details" className="block px-4 text-sm text-gray-300 hover:bg-[#252525] w-full">
                        <span className="pt-0">Personal Data</span>
                      </Link>
                      <Link href="/profile/change-password" className="block px-4 text-sm text-gray-300 hover:bg-[#252525] w-full">
                        <span className="pt-0">Change Password</span>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-[#252525] rounded-full py-1 px-1 flex items-center justify-start gap-3 min-w-[275px]">
                <div className="bg-[#1a1a1a] rounded-full p-2.5 flex items-center justify-center">
                  <Image src="/icons/balance-icon.svg" alt="Balance" width={20} height={20} className="text-white" style={{ filter: 'brightness(0) invert(1)' }} />
                </div>
                <div className="flex flex-col text-sm">
                  <div className="flex items-center gap-1">
                    <span className="text-gray-300">
                      <span className="font-bold">Balance</span> :
                    </span>

                    <span className="text-white-400 text-xs">
                      {userData.currency}. {typeof userData.balance === 'number' ? userData.balance.toFixed(2) : userData.balance}
                    </span>
                  </div>
                </div>
              </div>

              <button
  onClick={handleLogout}
  className="text-white hover:text-yellow-400 transition-colors"
>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="M19 12l-4-4m4 4-4 4m4-4H9m5 9a9 9 0 1 1 0-18"
    />
  </svg>
</button>

            </>
          ) : (
            <LoginDialog>
              <Button className="bg-yellow-500 text-black hover:bg-yellow-400 flex items-center gap-1">
                Login
              </Button>
            </LoginDialog>
          )}
        </div>
      </header>

      {/* Secondary Navigation with white separators - Hidden on mobile */}
      <div className="hidden md:flex text-sm px-4 py-2 border-b border-gray-800">
        <Link href="/" className={`${pathname === '/' ? 'text-white' : 'text-gray-300 hover:text-white'}`}>
          Home
        </Link>
        <span className="text-white mx-2">|</span>
        <Link href="/terms" className={`${pathname === '/terms' ? 'text-white' : 'text-gray-300 hover:text-white'}`}>
          Terms and Conditions
        </Link>
        <span className="text-white mx-2">|</span>
        <Link href="/affiliate" className={`${pathname === '/affiliate' ? 'text-white' : 'text-gray-300 hover:text-white'}`}>
          Affiliate
        </Link>
      </div>

      {/* Main Navigation - Hidden on mobile */}
      <div className="hidden md:block">
        <MainNavigation currentDate={currentDate} />
      </div>

      {/* Auto-scrollable Banner Gallery - Hidden on profile pages */}
      {!pathname.startsWith('/profile') && <BannerGallery />}
    </>
  )
}

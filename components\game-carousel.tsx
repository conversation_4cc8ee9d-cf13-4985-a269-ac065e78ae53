"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { ChevronLeft, ChevronRight } from "lucide-react"

// Define the game categories with the correct images
const gameCategories = [
  {
    id: "sport",
    title: "Sport",
    image: "/images/home-category-sports.png",
    href: "/sport",
  },
  {
    id: "live",
    title: "Live",
    image: "/images/home-category-live-match.png",
    href: "/live",
  },
  {
    id: "virtuals",
    title: "Virtuals",
    image: "/images/home-category-virtuals.png",
    href: "/virtuals",
  },
  {
    id: "spin2win",
    title: "Spin2Win",
    image: "/images/spin2win.jpg",
    href: "/spin2win",
  },
  {
    id: "lotto",
    title: "Lotto",
    image: "/images/home-category-lotto.jpg",
    href: "/lotto",
    style: {
      backgroundPosition: "left center",
      backgroundSize: "150% 100%",
    },
  },
  {
    id: "horse-racing",
    title: "Horse Racing",
    image: "/images/home-category-horse-racing.png",
    href: "/horse-racing",
    style: {
      backgroundPosition: "center center",
      backgroundSize: "100% 100%",
    },
  },
]

export default function GameCarousel() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const maxVisibleItems = useMaxVisibleItems()
  const totalItems = gameCategories.length

  // Determine how many items to show based on screen size
  function useMaxVisibleItems() {
    const [maxItems, setMaxItems] = useState(4)

    useEffect(() => {
      function handleResize() {
        if (window.innerWidth < 640) {
          setMaxItems(1)
        } else if (window.innerWidth < 768) {
          setMaxItems(2)
        } else if (window.innerWidth < 1024) {
          setMaxItems(3)
        } else {
          setMaxItems(4)
        }
      }

      handleResize()
      window.addEventListener("resize", handleResize)
      return () => window.removeEventListener("resize", handleResize)
    }, [])

    return maxItems
  }

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % (totalItems - maxVisibleItems + 1))
  }

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex === 0 ? totalItems - maxVisibleItems : prevIndex - 1))
  }

  // Auto-rotate carousel
  useEffect(() => {
    const interval = setInterval(() => {
      nextSlide()
    }, 5000)

    return () => clearInterval(interval)
  }, [currentIndex, maxVisibleItems])

  const visibleItems = []
  for (let i = 0; i < maxVisibleItems; i++) {
    const index = (currentIndex + i) % totalItems
    visibleItems.push(gameCategories[index])
  }

  return (
    <div className="relative">
      <div className="flex justify-between items-center">
        <button
          onClick={prevSlide}
          className="absolute left-0 z-10 bg-black/50 p-2 rounded-full text-yellow-500 hover:bg-black/70"
          aria-label="Previous slide"
        >
          <ChevronLeft className="h-6 w-6 sm:h-8 sm:w-8" />
        </button>

        <div className="flex gap-2 md:gap-4 overflow-hidden w-full px-8 md:px-12">
          {visibleItems.map((category) => (
            <Link
              key={category.id}
              href={category.href}
              className="relative group flex-1 aspect-[4/3] overflow-hidden rounded-lg"
            >
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent z-10" />
              <div className="absolute inset-0">
                <Image
                  src={category.image || "/placeholder.svg"}
                  alt={category.title}
                  fill
                  className="object-cover transition-transform duration-500 group-hover:scale-110"
                  style={category.style || {}}
                />
              </div>
              <div className="absolute bottom-2 md:bottom-4 left-0 right-0 text-center text-white text-sm md:text-xl font-bold z-20">
                {category.title}
              </div>
            </Link>
          ))}
        </div>

        <button
          onClick={nextSlide}
          className="absolute right-0 z-10 bg-black/50 p-2 rounded-full text-yellow-500 hover:bg-black/70"
          aria-label="Next slide"
        >
          <ChevronRight className="h-6 w-6 sm:h-8 sm:w-8" />
        </button>
      </div>
    </div>
  )
}

"use client"

import { usePathname } from "next/navigation"
import SiteHeader from "@/components/site-header"
import SiteFooter from "@/components/site-footer"
import { Toaster } from "sonner"
import LogoutButton from "@/components/LogoutButton"

export default function RootLayoutClient({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname();
  const showFooter = !pathname.startsWith('/casino');
  
  return (
    <>
      <SiteHeader />
      <main className="flex-1">{children}</main>
      {showFooter && <SiteFooter />}
      <Toaster position="top-right" richColors />
      <div className="hidden">
        <LogoutButton />
      </div>
    </>
  )
}

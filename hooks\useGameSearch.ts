import { useQuery } from '@tanstack/react-query';
import { gameService } from '@/services/game.service';
import { UseGameSearchParams } from '@/types/game.types';

export const useGameSearch = (params: UseGameSearchParams) => {
  return useQuery({
    queryKey: ['games', 'search', params],
    queryFn: () => gameService.searchGames(params),
    staleTime: 2 * 60 * 1000, // 2 minutes (shorter for search results)
    enabled: true, // Always enabled, can be conditional
  });
};

// Navigation items for mobile menu and other components
export interface NavItem {
  name: string;
  href: string;
  icon?: string;
}

export const navItems: NavItem[] = [
  {
    name: "Home",
    href: "/",
    icon: "/icons/home-icon.svg",
  },
  {
    name: "Sport",
    href: "/sport",
    icon: "/icons/sport-icon.svg",
  },
  {
    name: "Live",
    href: "/live",
    icon: "/icons/live-icon.svg",
  },
  {
    name: "Casino",
    href: "/casino",
    icon: "/icons/casino-icon.svg",
  },
  {
    name: "Livecasino",
    href: "/livecasino",
    icon: "/icons/live-icon.svg",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    href: "/roue-de-jeu",
    icon: "/icons/wheel-icon.svg",
  },
  {
    name: "Virtuals",
    href: "/virtuals",
    icon: "/icons/virtuals-icon.svg",
  },
  {
    name: "Lot<PERSON>",
    href: "/loto",
    icon: "/icons/loto-icon.svg",
  },
  {
    name: "Score en direct",
    href: "/score-en-direct",
    icon: "/icons/livescore-icon.svg",
  },
];

"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { userService, ChangePasswordRequest } from "@/services/user.service"
import { authUtils } from "@/utils/auth.utils"
import { authService } from "@/services/auth.service"
import { toast } from "sonner"

interface PasswordFormData {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export default function ChangePasswordPage() {
  const [formData, setFormData] = useState<PasswordFormData>({
    currentPassword: "",
    newPassword: "",
    confirmPassword: ""
  })

  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [userId, setUserId] = useState<number | null>(null)

  // Get user ID on component mount
  useEffect(() => {
    const userData = authUtils.getUserData()
    if (userData) {
      setUserId(userData.id)
    }
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    // Basic validation
    if (!formData.currentPassword || !formData.newPassword || !formData.confirmPassword) {
      setError("All fields are required")
      return
    }

    if (formData.newPassword !== formData.confirmPassword) {
      setError("New passwords do not match")
      return
    }

    // Password strength validation
    const passwordRegex = /^(?=.*[a-z])(?=.*\d).{8,}$/
    if (!passwordRegex.test(formData.newPassword)) {
      setError("New password must be at least 8 characters and contain at least one lowercase letter and one number")
      return
    }

    if (!userId) {
      setError("User ID not found. Please try logging in again.")
      return
    }

    setLoading(true)

    try {
      // Prepare request data
      const requestData: ChangePasswordRequest = {
        user_id: userId,
        old_password: formData.currentPassword,
        new_password: formData.newPassword,
        retype_password: formData.confirmPassword
      }

      // Call the API
      await userService.changePassword(requestData)

      // Show success message
      toast.success("Password changed successfully! Please login again with your new password.")

      // Reset form
      setFormData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: ""
      })

      // Set a small delay to ensure the toast is visible before redirecting
      setTimeout(async () => {
        // Log out the user
        await authService.logout()
        // Note: No need to redirect here as the logout method already redirects to the home page
      }, 2000)
    } catch (err) {
      // Show error message
      if (err instanceof Error) {
        setError(err.message)
      } else {
        setError("An unknown error occurred")
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="w-full p-4 bg-[#181818] text-white rounded-lg">
      <h1 className="text-base font-bold mb-3">Change your password</h1>

      {error && (
        <div className="mb-3 rounded-md bg-destructive/15 p-3 text-destructive text-xs">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="text-xs">
        <div className="mb-3">
          <div className="flex flex-col md:flex-row md:items-end gap-4">
            <div className="space-y-1 flex-1">
              <Label htmlFor="currentPassword" className="text-xs flex items-center">
                Current password <span className="text-red-500 ml-1">*</span>
              </Label>
              <Input
                id="currentPassword"
                type="password"
                value={formData.currentPassword}
                onChange={(e) => setFormData({ ...formData, currentPassword: e.target.value })}
                placeholder="Enter your current password"
                className="bg-[#313131] border-[#4F4F4F] h-8 text-xs"
                required
              />
            </div>

            <div className="space-y-1 flex-1">
              <Label htmlFor="newPassword" className="text-xs flex items-center">
                New password <span className="text-red-500 ml-1">*</span>
              </Label>
              <Input
                id="newPassword"
                type="password"
                value={formData.newPassword}
                onChange={(e) => setFormData({ ...formData, newPassword: e.target.value })}
                placeholder="Enter a new password"
                className="bg-[#313131] border-[#4F4F4F] h-8 text-xs"
                required
              />
            </div>

            <div className="space-y-1 flex-1">
              <Label htmlFor="confirmPassword" className="text-xs flex items-center">
                Confirm password <span className="text-red-500 ml-1">*</span>
              </Label>
              <Input
                id="confirmPassword"
                type="password"
                value={formData.confirmPassword}
                onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
                placeholder="Confirm new password"
                className="bg-[#313131] border-[#4F4F4F] h-8 text-xs"
                required
              />
            </div>

            <div className="md:ml-auto">
              <Button
                type="submit"
                className="px-4 bg-emerald-600 hover:bg-emerald-700 text-white text-xs h-8"
                disabled={loading}
              >
                {loading ? "Saving..." : "Save"}
              </Button>
            </div>
          </div>
        </div>
      </form>
    </div>
  )
}

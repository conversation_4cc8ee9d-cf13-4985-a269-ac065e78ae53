"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"

interface NavItemProps {
  href: string
  label: string
  isLast?: boolean
}

export function NavItem({ href, label, isLast = false }: NavItemProps) {
  const pathname = usePathname()
  const isActive = pathname === href

  return (
    <Link
      href={href}
      className={`block py-1.5 px-2 text-xs hover:bg-[#FFCC00] hover:text-black transition-colors ${isActive ? 'bg-[#FFCC00] text-black' : 'text-gray-200'} ${!isLast ? 'border-b border-[#2a2a2a]' : ''}`}
    >
      {label}
    </Link>
  )
}

"use client"

import type React from "react"
import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { authService } from "@/services/auth.service"
import { authUtils } from "@/utils/auth.utils"
import { toast } from "sonner"

interface LoginFormProps {
  onLoginSuccess?: () => void;
}

export default function LoginForm({ onLoginSuccess }: LoginFormProps) {
  const [formData, setFormData] = useState({
    username: "",
    password: "",
    rememberMe: false
  })
  const [error, setError] = useState("")
  const [loading, setLoading] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setLoading(true)

    try {
      const response = await authService.login(formData.username, formData.password)

      if (response.status === 'success' && response.data) {
        const { user } = response.data

        // For player frontend, we only allow players to login
        if (user.role !== 'player') {
          authUtils.clearAuthData()
          setError('Admin users are not allowed to access the player panel.')
          setLoading(false)
          return
        }

        try {
          // Store user data only (tokens are in HTTP-only cookies)
          authUtils.setUserData({
            ...user,
            balance: user.balance.toString()
          })

          // Set up automatic token refresh
          await authUtils.setupTokenRefresh()

          // Clear sensitive form data
          setFormData({ username: '', password: '', rememberMe: false })

          // Show success message
          toast.success('Login successful!')

          // Call onLoginSuccess callback if provided
          if (onLoginSuccess) {
            onLoginSuccess();
          }

          // Force navigation to home page
          window.location.href = '/'
        } catch (error) {
          console.error('Error storing user data:', error)
          setError('Failed to store authentication data. Please try again.')
          authUtils.clearAuthData()
          setLoading(false)
        }
      } else {
        throw new Error('Invalid response from server')
      }
    } catch (err: any) {
      // Handle different error scenarios
      if (err.response?.status === 401) {
        setError('Invalid username or password')
      } else if (err.response?.data?.message) {
        setError(err.response.data.message)
      } else if (err.message) {
        setError(err.message)
      } else {
        setError('An error occurred during login. Please try again.')
      }
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && (
        <div className="bg-red-500/20 border border-red-500 text-red-100 px-4 py-2 rounded-md text-sm">
          {error}
        </div>
      )}

      <div className="flex items-center space-x-3 rounded-md border border-gray-700 bg-gray-800 px-3 py-2">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="text-gray-400"
        >
          <circle cx="12" cy="8" r="5" />
          <path d="M20 21a8 8 0 0 0-16 0" />
        </svg>
        <Input
          type="text"
          name="username"
          value={formData.username}
          onChange={handleChange}
          placeholder="Username"
          className="border-0 bg-transparent p-0 text-white placeholder-gray-400 focus-visible:ring-0 focus-visible:ring-offset-0"
          disabled={loading}
        />
      </div>

      <div className="flex items-center space-x-3 rounded-md border border-gray-700 bg-gray-800 px-3 py-2">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="text-gray-400"
        >
          <rect width="18" height="11" x="3" y="11" rx="2" ry="2" />
          <path d="M7 11V7a5 5 0 0 1 10 0v4" />
        </svg>
        <Input
          type="password"
          name="password"
          value={formData.password}
          onChange={handleChange}
          placeholder="Password"
          className="border-0 bg-transparent p-0 text-white placeholder-gray-400 focus-visible:ring-0 focus-visible:ring-offset-0"
          disabled={loading}
        />
      </div>

      <Button
        type="submit"
        className="w-full bg-green-500 py-6 text-lg font-medium hover:bg-green-600"
        disabled={loading}
      >
        {loading ? "Signing in..." : "Login"}
      </Button>
    </form>
  )
}

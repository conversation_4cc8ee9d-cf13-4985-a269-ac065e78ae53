"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

// Mock data for betting history
const mockBettingHistory = [
  {
    id: "1",
    date: "2023-05-15",
    event: "PSG vs Marseille",
    bet: "PSG Win",
    amount: "50",
    odds: "1.75",
    result: "Win",
    payout: "87.50"
  },
  {
    id: "2",
    date: "2023-05-10",
    event: "Lyon vs Monaco",
    bet: "Over 2.5 Goals",
    amount: "30",
    odds: "1.95",
    result: "Loss",
    payout: "0"
  },
  {
    id: "3",
    date: "2023-05-05",
    event: "Lille vs Rennes",
    bet: "Draw",
    amount: "25",
    odds: "3.20",
    result: "Win",
    payout: "80.00"
  }
]

export default function BettingHistoryPage() {
  const [dateRange, setDateRange] = useState({
    from: "",
    to: ""
  })

  const [filteredHistory, setFilteredHistory] = useState(mockBettingHistory)

  const handleFilter = (e: React.FormEvent) => {
    e.preventDefault()

    // In a real app, you would fetch filtered data from an API
    // For now, we'll just simulate filtering
    console.log("Filtering with date range:", dateRange)

    // Simple mock filtering logic
    if (!dateRange.from && !dateRange.to) {
      setFilteredHistory(mockBettingHistory)
      return
    }

    const filtered = mockBettingHistory.filter(bet => {
      const betDate = new Date(bet.date)
      const fromDate = dateRange.from ? new Date(dateRange.from) : new Date(0)
      const toDate = dateRange.to ? new Date(dateRange.to) : new Date(9999, 11, 31)

      return betDate >= fromDate && betDate <= toDate
    })

    setFilteredHistory(filtered)
  }

  return (
    <div className="max-w-6xl">
      <h1 className="mb-4 text-xl font-bold text-[#CAD5D5]">Historique des paris</h1>

      <form onSubmit={handleFilter} className="mb-6 space-y-4">
        <div className="flex flex-col space-y-4 md:flex-row md:space-x-4 md:space-y-0">
          <div className="space-y-2 md:w-1/3">
            <Label htmlFor="dateFrom">Date de début</Label>
            <Input
              id="dateFrom"
              type="date"
              value={dateRange.from}
              onChange={(e) => setDateRange({ ...dateRange, from: e.target.value })}
              className="bg-[#252525] border-[#2a2a2a] text-gray-300"
            />
          </div>

          <div className="space-y-2 md:w-1/3">
            <Label htmlFor="dateTo">Date de fin</Label>
            <Input
              id="dateTo"
              type="date"
              value={dateRange.to}
              onChange={(e) => setDateRange({ ...dateRange, to: e.target.value })}
              className="bg-[#252525] border-[#2a2a2a] text-gray-300"
            />
          </div>

          <div className="flex items-end md:w-1/3">
            <Button type="submit" className="w-full bg-[#FFCC00] text-black hover:bg-[#FFCC00]/90">
              Filtrer
            </Button>
          </div>
        </div>
      </form>

      <div className="overflow-x-auto rounded-lg border border-[#2a2a2a]">
        <table className="w-full">
          <thead className="bg-[#252525]">
            <tr>
              <th className="whitespace-nowrap px-3 py-2 text-left text-sm font-medium">Date</th>
              <th className="whitespace-nowrap px-3 py-2 text-left text-sm font-medium">Événement</th>
              <th className="whitespace-nowrap px-3 py-2 text-left text-sm font-medium">Pari</th>
              <th className="whitespace-nowrap px-3 py-2 text-left text-sm font-medium">Montant</th>
              <th className="whitespace-nowrap px-3 py-2 text-left text-sm font-medium">Cotes</th>
              <th className="whitespace-nowrap px-3 py-2 text-left text-sm font-medium">Résultat</th>
              <th className="whitespace-nowrap px-3 py-2 text-left text-sm font-medium">Gain</th>
            </tr>
          </thead>
          <tbody className="divide-y">
            {filteredHistory.length > 0 ? (
              filteredHistory.map((bet) => (
                <tr key={bet.id} className="hover:bg-muted/50">
                  <td className="whitespace-nowrap px-3 py-2 text-sm">{bet.date}</td>
                  <td className="whitespace-nowrap px-3 py-2 text-sm">{bet.event}</td>
                  <td className="whitespace-nowrap px-3 py-2 text-sm">{bet.bet}</td>
                  <td className="whitespace-nowrap px-3 py-2 text-sm">{bet.amount} €</td>
                  <td className="whitespace-nowrap px-3 py-2 text-sm">{bet.odds}</td>
                  <td className="whitespace-nowrap px-3 py-2 text-sm">
                    <span className={bet.result === "Win" ? "text-green-500" : "text-red-500"}>
                      {bet.result}
                    </span>
                  </td>
                  <td className="whitespace-nowrap px-3 py-2 text-sm">{bet.payout} €</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="px-3 py-2 text-sm text-center">Aucun pari trouvé pour cette période</td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  )
}

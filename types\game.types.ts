// Game API Types based on game-api.md documentation

export interface Game {
  id: string;
  name: string;
  title: string;
  img: string;
  device: string;
  categories: string;
  flash: boolean;
}

export interface GameSearchParams {
  name?: string;           // Game name search
  title?: string;          // Game title search
  categories?: string;     // Comma-separated categories
  device?: string;         // Device type filter
  page?: number;           // Page number (default: 1)
  limit?: number;          // Items per page (default: 20, max: 100)
  sortBy?: 'name' | 'title'; // Sort column (default: 'name')
  sortOrder?: 'ASC' | 'DESC'; // Sort order (default: 'ASC')
}

export interface GameSearchResponse {
  games: Game[];
  pagination: {
    total: number;
    page: number;
    totalPages: number;
    limit: number;
  };
}

export interface GameUrlResponse {
  url: string;
}

export interface GameHistoryParams {
  page?: number;
  limit?: number;
  startDate?: string; // ISO date
  endDate?: string;   // ISO date
  type?: 'add' | 'deduct'; // Transaction type filter
}

// Frontend-specific types
export interface UseGameSearchParams {
  name?: string;
  title?: string;
  categories?: string[];
  device?: string;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'title';
  sortOrder?: 'ASC' | 'DESC';
}

export type CategoriesResponse = string[];
export type TitlesResponse = string[];

// Legacy interface for backward compatibility with existing casino page
export interface LegacyGame {
  id: number;
  title: string;
  image: string;
}

// Favorites API Types based on favorites.md documentation
export interface FavoritesResponse {
  status: string;
  message: string;
  data: {
    favorites: Game[];
    count: number;
  };
}

export interface AddFavoriteResponse {
  status: string;
  message: string;
  data: {
    gameId: string;
    userId: number;
  };
}

export interface RemoveFavoriteResponse {
  status: string;
  message: string;
  data: {
    gameId: string;
    userId: number;
  };
}

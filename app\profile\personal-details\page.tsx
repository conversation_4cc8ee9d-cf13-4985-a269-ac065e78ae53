"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { authUtils } from "@/utils/auth.utils"

interface FormData {
  id: number
  username: string
  firstName: string
  lastName: string
  address: string
  phoneNumber: string
  currency: string
}

export default function PersonalDetailsPage() {
  const [formData, setFormData] = useState<FormData>({
    id: 1,
    username: "",
    firstName: "",
    lastName: "",
    address: "",
    phoneNumber: "",
    currency: ""
  })

  useEffect(() => {
    // In a real app, you would fetch this data from an API
    const userData = authUtils.getUserData()
    if (userData) {
      setFormData({
        id: userData.id || 1,
        username: userData.username || "",
        firstName: "",
        lastName: "",
        address: "",
        phoneNumber: "",
        currency: userData.currency || ""
      })
    }
  }, [])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // In a real app, you would submit this data to an API
  }

  return (
    <div className="w-full p-4 bg-[#121212] text-white rounded-lg">
      <h1 className="text-base font-bold mb-3">Personal details</h1>

      <form onSubmit={handleSubmit} className="text-xs">
        {/* Personal information section */}
        <div className="mb-3">
          <h2 className="text-xs font-medium mb-2">Personal information</h2>
          <div className="border-t border-zinc-800 my-3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-1">
              <Label htmlFor="id" className="text-xs">
                id
              </Label>
              <Input
                id="id"
                name="id"
                value={formData.id}
                onChange={handleChange}
                className="bg-[#121212] border-[#4F4F4F] h-8 text-xs"
                disabled
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="username" className="text-xs">
                Username
              </Label>
              <Input
                id="username"
                name="username"
                value={formData.username}
                onChange={handleChange}
                className="bg-[#121212] border-[#4F4F4F] h-8 text-xs"
                disabled
              />
            </div>
          </div>
        </div>
        <div className="border-t border-[#4F4F4F] my-3"></div>

        {/* Personal data section */}
        <div className="mb-3">
          <h2 className="text-xs font-medium mb-2">Personal data</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
            <div className="space-y-1">
              <Label htmlFor="firstName" className="text-xs">
                First name
              </Label>
              <Input
                id="firstName"
                name="firstName"
                placeholder="First name"
                value={formData.firstName}
                onChange={handleChange}
                className="bg-[#121212] border-[#4F4F4F] h-8 text-xs"
                disabled
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="lastName" className="text-xs">
                Last name
              </Label>
              <Input
                id="lastName"
                name="lastName"
                placeholder="Last name"
                value={formData.lastName}
                onChange={handleChange}
                className="bg-[#121212] border-[#4F4F4F] h-8 text-xs"
                disabled
              />
            </div>
          </div>
          <div className="space-y-1">
            <Label htmlFor="address" className="text-xs">
              Address
            </Label>
            <Input
              id="address"
              name="address"
              placeholder="Address"
              value={formData.address}
              onChange={handleChange}
              className="bg-[#121212] border-[#4F4F4F] h-8 text-xs"
              disabled
            />
          </div>
        </div>
        <div className="border-t border-[#4F4F4F] my-3"></div>

        {/* Contact details section */}
        <div className="mb-3">
          <h2 className="text-xs font-medium mb-2">Contact details</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-1">
              <Label htmlFor="phoneNumber" className="text-xs">
                Phone number
              </Label>
              <Input
                id="phoneNumber"
                name="phoneNumber"
                placeholder="Phone number"
                value={formData.phoneNumber}
                onChange={handleChange}
                className="bg-[#121212] border-[#4F4F4F] h-8 text-xs"
                disabled
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="currency" className="text-xs">
                Currency
              </Label>
              <Input
                id="currency"
                name="currency"
                value={formData.currency}
                onChange={handleChange}
                className="bg-[#121212] border-[#4F4F4F] h-8 text-xs"
                disabled
              />
            </div>
          </div>
        </div>
        <div className="border-t border-[#4F4F4F] my-3"></div>

        {/* Save button - kept for visual consistency but disabled since all fields are read-only */}
        <Button
          type="button"
          className="w-full bg-emerald-600 hover:bg-emerald-700 text-white text-xs py-2"
          disabled
        >
          Save
        </Button>
      </form>
    </div>
  )
}

'use client';

import { But<PERSON> } from "@/components/ui/button";
import { authService } from "@/services/auth.service";
import { authUtils } from "@/utils/auth.utils";
import { toast } from "sonner";
import { LogOut } from "lucide-react";

interface LogoutButtonProps {
  onLogout?: () => void;
  className?: string;
}

export default function LogoutButton({ onLogout, className = "" }: LogoutButtonProps) {
  const handleLogout = async () => {
    try {
      await authService.logout();
      // Redirect is handled in the logout method
      if (onLogout) {
        onLogout();
      }
    } catch (error) {
      console.error('Logout failed:', error);
      // Fallback redirect
      toast.error('Logout failed. Please try again.');
      authUtils.clearAuthData();
      window.location.href = '/';
    }
  };

  return (
    <Button
      onClick={handleLogout}
      className={`flex items-center space-x-2 text-red-500 hover:text-red-700 ${className}`}
      variant="ghost"
      data-logout-button
    >
      <LogOut className="h-4 w-4" />
      <span>Logout</span>
    </Button>
  );
}

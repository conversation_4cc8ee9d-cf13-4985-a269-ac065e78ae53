import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// This array contains paths that don't require authentication
const publicPaths = [
  '/',
  '/login',
  '/api/users/refresh-token',
  '/games',
  '/about',
  '/contact',
  '/terms',
  '/affiliate',
  '/sport',
  '/live',
  '/casino',
  '/livecasino',
  '/virtuals',
  '/evolution',
  '/roulettes',
  '/blackjack',
  '/spin2win',
  '/horse-racing',
  '/lotto',
  '/aviator',
  '/roue-de-jeu',
  '/score-en-direct'
];

// Function to check if a path is public
const isPublicPath = (path: string) => {
  return publicPaths.some(publicPath => 
    path === publicPath || 
    path.startsWith(`${publicPath}/`)
  );
};

export function middleware(request: NextRequest) {
  // Bypass middleware for static files
  if (/\.[^/]+$/.test(request.nextUrl.pathname)) {
    return NextResponse.next();
  }

  const accessToken = request.cookies.get('accessToken');
  const refreshToken = request.cookies.get('refreshToken');
  const currentPath = request.nextUrl.pathname;
  const domain = request.nextUrl.hostname;

  const hasAccessToken = !!accessToken;
  const hasRefreshToken = !!refreshToken;

  // Allow access to public paths without authentication
  if (isPublicPath(currentPath)) {
    return NextResponse.next();
  }

  // If user has tokens and tries to access login page, redirect to home
  if ((hasAccessToken || hasRefreshToken) && currentPath === '/login') {
    const homeUrl = new URL('/home', request.url);
    homeUrl.hostname = domain;
    return NextResponse.redirect(homeUrl);
  }

  // If no tokens at all and trying to access protected route, redirect to login
  if (!hasAccessToken && !hasRefreshToken && !isPublicPath(currentPath)) {
    const loginUrl = new URL('/login', request.url);
    loginUrl.hostname = domain;
    loginUrl.searchParams.set('from', currentPath);
    return NextResponse.redirect(loginUrl);
  }

  // Allow access to all other routes - the backend will validate the tokens
  return NextResponse.next();
}

// Configure which paths should be processed by middleware
export const config = {
  matcher: [
    /*
     * Match all paths except:
     * 1. /_next (Next.js internals)
     * 2. /_static (inside /public)
     * 3. /_vercel (Vercel internals)
     * 4. /favicon.ico, /sitemap.xml (static files)
     */
    '/((?!_next|_static|_vercel|favicon.ico|sitemap.xml).*)',
  ],
};

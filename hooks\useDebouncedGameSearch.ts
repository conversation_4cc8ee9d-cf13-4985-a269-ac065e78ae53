import { useMemo } from 'react';
import { useDebounce } from 'use-debounce';
import { useGameSearch } from './useGameSearch';
import { UseGameSearchParams } from '@/types/game.types';

export const useDebouncedGameSearch = (searchTerm: string, delay = 300) => {
  const [debouncedSearchTerm] = useDebounce(searchTerm, delay);

  const searchParams: UseGameSearchParams = useMemo(() => ({
    name: debouncedSearchTerm || undefined,
    page: 1,
    limit: 20,
  }), [debouncedSearchTerm]);

  return useGameSearch(searchParams);
};

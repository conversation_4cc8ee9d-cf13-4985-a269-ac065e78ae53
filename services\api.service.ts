import axios from 'axios';
import { authUtils } from '@/utils/auth.utils';
import { authService } from '@/services/auth.service';
import { toast } from 'sonner';
import { API_BASE_URL } from '@/config';

// Create axios instance with interceptors
export const createApiClient = (baseUrl: string) => {
  const apiClient = axios.create({
    baseURL: baseUrl,
    withCredentials: true, // Important for cookies
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    timeout: 10000
  });

  // Add request interceptor to include CSRF token
  apiClient.interceptors.request.use(
    async config => {
      // Skip for GET requests
      if (config.method?.toLowerCase() === 'get') {
        return config;
      }

      // For non-GET requests, ensure we have a valid token
      // Skip for login and refresh-token endpoints
      if (!config.url?.includes('refresh-token') && !config.url?.includes('login')) {
        await authUtils.ensureValidToken();
      }

      // Add CSRF token to headers for non-GET requests
      const csrfToken = authUtils.getCsrfToken();
      if (csrfToken) {
        config.headers['X-CSRF-Token'] = csrfToken;
      }

      return config;
    },
    error => Promise.reject(error)
  );

  // Add response interceptor
  apiClient.interceptors.response.use(
    response => response,
    async error => {
      // Skip 401 handling for login endpoint
      if (error.config?.url === '/login' && error.response?.status === 401) {
        return Promise.reject(error);
      }

      // Handle 401 errors (unauthorized)
      if (error.response?.status === 401) {
        // Try to refresh the token if not already trying to refresh
        if (!error.config?.url?.includes('refresh-token')) {
          try {
            await authService.refreshToken();

            // Retry the original request
            const newConfig = { ...error.config };

            // Update CSRF token for the retry
            if (newConfig.method?.toLowerCase() !== 'get') {
              const csrfToken = authUtils.getCsrfToken();
              if (csrfToken) {
                newConfig.headers['X-CSRF-Token'] = csrfToken;
              }
            }

            return axios(newConfig);
          } catch (refreshError) {
            // Clear auth data but don't redirect if we're already on the login page
            authUtils.clearAuthData();

            // Only redirect if we're not already on the login page
            const isLoginPage = window.location.pathname === '/' ||
                              window.location.pathname === '/en' ||
                              window.location.pathname === '/fr';

            if (!isLoginPage) {
              toast.error('Session expired. Please login again.');
              window.location.href = '/';
            }

            return Promise.reject(refreshError);
          }
        }

        // If we're already trying to refresh, just clear auth and redirect if needed
        authUtils.clearAuthData();

        // Only redirect if we're not already on the login page
        const isLoginPage = window.location.pathname === '/' ||
                          window.location.pathname === '/en' ||
                          window.location.pathname === '/fr';

        if (!isLoginPage) {
          toast.error('Session expired. Please login again.');
          window.location.href = '/';
        }
      }

      return Promise.reject(error);
    }
  );

  return apiClient;
};

// Create a default API client
export const apiClient = createApiClient(`${API_BASE_URL}/api`);

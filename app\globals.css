@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 255, 255, 255;
  --background-rgb: 0, 0, 0;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-rgb));
  font-family: Arial, Helvetica, sans-serif;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .yellow-button {
    @apply bg-yellow-500 text-black hover:bg-yellow-400 transition-colors;
  }

  /* Menu item styling */
  .menu-item {
    @apply flex flex-col items-center text-center justify-center;
  }

  /* Icon wrapper for consistent sizing */
  .icon-wrapper {
    @apply mb-0.5;
  }

  /* Swipable container for mobile navigation */
  .swipable-nav {
    @apply flex overflow-x-auto py-1 px-2;
    -webkit-overflow-scrolling: touch; /* for smooth scrolling on iOS */
    scrollbar-width: none; /* hide scrollbar in Firefox */
    -ms-overflow-style: none; /* hide scrollbar in IE/Edge */
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .swipable-nav::-webkit-scrollbar {
    display: none;
  }

  /* Fixed width for nav items in swipable container */
  .nav-item-fixed {
    @apply flex-none;
    /* Width now controlled by grid layout */
  }

  /* Scroll indicator container */
  .scroll-indicator-container {
    @apply h-0.5 bg-gray-800 relative mt-0.5 mb-1 overflow-hidden;
  }

  /* Scroll indicator bar */
  .scroll-indicator-bar {
    @apply h-full bg-yellow-500 absolute left-0 top-0;
    transition: width 0.1s ease, transform 0.1s ease;
  }

  /* Simple hover menu system */
  .menu-item {
    position: relative;
    width: 100%;
  }

  .menu-item:hover .submenu {
    display: block;
  }

  .submenu {
    display: none;
    position: absolute;
    left: 100%;
    top: 0;
    width: 275px; /* Same width as dropdown-content */
    background-color: #0f0f0f; /* Match header background */
    border-radius: 0.375rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    z-index: 50;
  }

  /* Align submenu items to the top */
  .submenu a {
    padding-top: 8px;
    padding-bottom: 8px;
    display: flex;
    align-items: flex-start;
  }

  /* Logout button hover effect */
  .logout-btn img {
    filter: brightness(0) invert(1);
    transition: filter 0.2s ease;
  }

  .logout-btn:hover img {
    filter: brightness(0) saturate(100%) invert(88%) sepia(54%) saturate(1636%) hue-rotate(359deg) brightness(105%) contrast(101%);
  }

  /* Casino page specific styles */
  .scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }

  .fixed-width-dropdown {
    width: 100vw;
    left: 0;
    position: fixed;
    top: 50%;
    transform: translateY(-50%);
  }

  /* Dark scrollbar for dropdowns */
  .dark-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #4F4F4F #181818;
  }

  .dark-scrollbar::-webkit-scrollbar {
    width: 8px;
  }

  .dark-scrollbar::-webkit-scrollbar-track {
    background: #181818;
  }

  .dark-scrollbar::-webkit-scrollbar-thumb {
    background: #4F4F4F;
    border-radius: 4px;
  }

  .dark-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #6B6B6B;
  }
}

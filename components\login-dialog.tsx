"use client"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import LoginForm from "@/components/login-form"
import { useMediaQuery } from "@/hooks/use-media-query"
import { useRouter } from "next/navigation"
import { authUtils } from "@/utils/auth.utils"

interface LoginDialogProps {
  children: React.ReactNode
}

export function LoginDialog({ children }: LoginDialogProps) {
  const [open, setOpen] = useState(false)
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const isDesktop = useMediaQuery("(min-width: 768px)")
  const router = useRouter()

  useEffect(() => {
    // Check if user is logged in
    const userData = authUtils.getUserData()
    setIsLoggedIn(!!userData)
  }, [])

  const handleOpenChange = (open: boolean) => {
    if (!isDesktop && open) {
      // On mobile, redirect to the login page instead of showing dialog
      router.push("/login")
      return
    }
    setOpen(open)
  }

  // If user is logged in, don't render the login dialog
  if (isLoggedIn) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      {isDesktop && (
        <DialogContent className="sm:max-w-md bg-black border-gray-800">
          <DialogHeader>
            <DialogTitle className="text-center text-xl font-bold text-white">Sign In</DialogTitle>
          </DialogHeader>
          <LoginForm onLoginSuccess={() => setOpen(false)} />
        </DialogContent>
      )}
    </Dialog>
  )
}

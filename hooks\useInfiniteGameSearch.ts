import { useInfiniteQuery } from '@tanstack/react-query';
import { gameService } from '@/services/game.service';
import { UseGameSearchParams } from '@/types/game.types';

export const useInfiniteGameSearch = (filters: Omit<UseGameSearchParams, 'page'>) => {
  return useInfiniteQuery({
    queryKey: ['games', 'infinite', filters],
    queryFn: async ({ pageParam = 1 }) => {
      return gameService.searchGames({
        ...filters,
        page: pageParam,
        limit: 20
      });
    },
    getNextPageParam: (lastPage) => {
      const { page, totalPages } = lastPage.pagination;
      return page < totalPages ? page + 1 : undefined;
    },
    initialPageParam: 1,
    staleTime: 2 * 60 * 1000,
  });
};

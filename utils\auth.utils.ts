import { AES, enc } from 'crypto-js';

const ENCRYPTION_KEY = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || 'africax1-secure-key';

// Helper to check if we're on the client side
const isClient = typeof window !== 'undefined';

// Token refresh interval in milliseconds (14 minutes)
const TOKEN_REFRESH_INTERVAL = 14 * 60 * 1000;

// Last token refresh timestamp
let lastTokenRefresh = 0;

// Token refresh timer
let refreshTimer: NodeJS.Timeout | null = null;

// Define the type for authService to avoid circular dependency
interface RefreshTokenResponse {
  status: string;
  data: {
    csrfToken: string;
  };
}

type AuthServiceType = {
  refreshToken: () => Promise<RefreshTokenResponse>;
};

// Will be initialized later to avoid circular dependency
let authService: AuthServiceType | null = null;

export interface User {
  id: number;
  username: string;
  role: string;
  balance: string | number;
  currency: string;
}

export const authUtils = {
  // Set up automatic token refresh
  setupTokenRefresh: async () => {
    if (!isClient) return;

    // Clear any existing timer
    if (refreshTimer) {
      clearInterval(refreshTimer);
    }

    // Initialize authService if needed
    if (!authService) {
      try {
        const authServiceModule = await import('@/services/auth.service');
        authService = authServiceModule.authService;
      } catch (error) {
        return;
      }
    }

    // Immediately refresh the token once when setting up
    if (authService && authUtils.getUserData()) {
      try {
        await authService.refreshToken();
      } catch (error) {
        // Silent fail
      }
    }

    // Set up a new timer to refresh the token every 2 minutes
    refreshTimer = setInterval(async () => {
      try {
        // Only refresh if we have authService and user is logged in
        if (authService && authUtils.getUserData()) {
          await authService.refreshToken();
        }
      } catch (error) {
        // Silent fail
      }
    }, TOKEN_REFRESH_INTERVAL);
  },

  // Clear token refresh timer
  clearTokenRefresh: () => {
    if (!isClient) return;

    if (refreshTimer) {
      clearInterval(refreshTimer);
      refreshTimer = null;
    }
  },
  // Store user data only (no tokens)
  setUserData: (user: User) => {
    if (!isClient) return;

    try {
      // Encrypt and store user data
      const encryptedUserData = AES.encrypt(
        JSON.stringify(user),
        ENCRYPTION_KEY
      ).toString();

      // Store encrypted user data
      window.localStorage.setItem('userDataSite', encryptedUserData);
    } catch (error) {
      // Silent fail
      throw new Error('Failed to store user data');
    }
  },

  // Get user data from localStorage
  getUserData: (): User | null => {
    if (!isClient) return null;

    try {
      const encryptedUserData = window.localStorage.getItem('userDataSite');
      if (!encryptedUserData) {
        return null;
      }

      // Decrypt user data
      const decryptedBytes = AES.decrypt(encryptedUserData, ENCRYPTION_KEY);
      const userData = JSON.parse(decryptedBytes.toString(enc.Utf8));

      return userData;
    } catch (error) {
      // Silent fail
      return null;
    }
  },

  // Update user data
  updateUserData: (userData: Partial<User>) => {
    if (!isClient) return;

    try {
      const currentUserData = authUtils.getUserData();
      if (!currentUserData) {
        throw new Error('No user data found');
      }

      // Update user data
      const updatedUserData = {
        ...currentUserData,
        ...userData
      };

      // Store updated user data
      authUtils.setUserData(updatedUserData);
    } catch (error) {
      // Silent fail
    }
  },

  // Get CSRF token from cookie
  getCsrfToken: (): string | null => {
    if (!isClient) return null;

    const cookies = document.cookie.split(';');
    const csrfCookie = cookies.find(c => c.trim().startsWith('csrf_token='));
    if (!csrfCookie) {
      return null;
    }

    return decodeURIComponent(csrfCookie.split('=')[1].trim());
  },

  // Function to ensure we have a valid token
  ensureValidToken: async (): Promise<boolean> => {
    try {
      // Initialize authService if needed
      await new Promise<void>((resolve) => {
        if (authService) {
          resolve();
          return;
        }

        // Use dynamic import
        import('@/services/auth.service').then(module => {
          authService = module.authService;
          resolve();
        }).catch(() => {
          resolve(); // Resolve anyway to continue execution
        });
      });

      // If authService is still null after initialization, throw an error
      if (!authService) {
        throw new Error('Failed to initialize authService');
      }

      const now = Date.now();
      const timeSinceLastRefresh = now - lastTokenRefresh;

      // If we don't have a CSRF token or it's time to refresh (14 minutes have passed)
      if (!authUtils.getCsrfToken() || timeSinceLastRefresh >= TOKEN_REFRESH_INTERVAL) {
        await authService.refreshToken();
        lastTokenRefresh = now;
        return true;
      }

      return true;
    } catch (error) {
      // Only clear auth data if we're not on the login page
      if (typeof window !== 'undefined' &&
          window.location.pathname !== '/' &&
          window.location.pathname !== '/en' &&
          window.location.pathname !== '/fr') {
        authUtils.clearAuthData();
      }
      return false;
    }
  },

  // Store CSRF token in cookie only
  storeCsrfToken: (token: string): void => {
    if (!isClient || !token) return;

    try {
      // Set as cookie only - never store in localStorage
      document.cookie = `csrf_token=${token}; path=/; max-age=86400; samesite=lax`;
      // Update last token refresh timestamp
      lastTokenRefresh = Date.now();
    } catch (error) {
      // Silent fail
    }
  },

  clearAuthData: () => {
    if (!isClient) return;

    try {
      // Clear user data from localStorage
      window.localStorage.removeItem('userDataSite');

      // Clear csrf_token cookie
      document.cookie = 'csrf_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; samesite=lax';

      // Clear token refresh timer
      authUtils.clearTokenRefresh();
    } catch (error) {
      // Silent fail
    }
  },

  isAuthenticated: async (): Promise<boolean> => {
    if (!isClient) return false;

    const userData = authUtils.getUserData();

    if (!userData) {
      return false;
    }

    // Ensure we have a valid token
    return await authUtils.ensureValidToken();
  },
};

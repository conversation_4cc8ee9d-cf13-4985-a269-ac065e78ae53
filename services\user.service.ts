import { apiClient } from '@/services/api.service';
import { authUtils } from '@/utils/auth.utils';
import { toast } from 'sonner';

export interface UserDetailsResponse {
  status: string;
  data: {
    id: number;
    username: string;
    role: string;
    balance: number;
    currency: string;
    is_active: boolean;
    is_banned: boolean;
    last_login: string;
    createdAt: string;
  };
}

export interface ChangePasswordRequest {
  user_id: number;
  old_password: string;
  new_password: string;
  retype_password: string;
}

export interface ChangePasswordResponse {
  status: string;
  message: string;
}

export const userService = {
  async getProfile(): Promise<UserDetailsResponse> {
    try {
      // Use the correct endpoint as shown in the backend code
      const response = await apiClient.get('/users/details');

      // Update the user data in localStorage with the new balance
      if (response.data?.status === 'success' && response.data?.data) {
        const userData = authUtils.getUserData();
        if (userData) {
          authUtils.updateUserData({
            balance: response.data.data.balance,
            currency: response.data.data.currency
          });
        }
      }

      return response.data;
    } catch (error: any) {
      // If the error is not a 401 (which is handled by the interceptor)
      // we don't want to show an error message for balance checks
      if (error?.response?.status === 401) {
        throw error;
      }

      // For other errors, we'll just log them but not show a toast
      console.error('Failed to get user profile:', error);
      throw error;
    }
  },

  async changePassword(data: ChangePasswordRequest): Promise<ChangePasswordResponse> {
    try {
      const response = await apiClient.post('/users/password', data);
      return response.data;
    } catch (error: any) {
      // Extract error message from response if available
      const errorMessage = error.response?.data?.message || 'Failed to change password';

      // Log the error
      console.error('Change password error:', error);

      // Throw the error with the message
      throw new Error(errorMessage);
    }
  }
};

"use client"

import React from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { Button } from "@/components/ui/button"
import { useMenuContext } from "@/contexts/menu-context"


interface MobileMenuProps {
  navItems: { name: string; href: string; icon?: string }[]
}

interface NavItemProps {
  icon: React.ReactNode
  label: string
  href: string
  onClick: () => void
  isActive?: boolean
}

const NavItem = ({ icon, label, href, onClick, isActive = false }: NavItemProps) => {
  return (
    <Link href={href} onClick={onClick} className="block relative">
      <div className={`flex flex-col items-center justify-center py-4 cursor-pointer ${isActive ? 'bg-[#2a2a2a]' : 'hover:bg-[#2a2a2a]'}`}>
        <div className={`mb-1 ${isActive ? 'text-yellow-500' : 'text-gray-300'}`}>{icon}</div>
        <span className={`text-xs ${isActive ? 'text-yellow-500' : 'text-gray-300'}`}>{label}</span>
        {isActive && <div className="absolute bottom-0 left-0 right-0 h-[3px] bg-yellow-500"></div>}
      </div>
    </Link>
  )
}

export default function MobileMenu({ navItems }: MobileMenuProps) {
  // Use the shared menu context
  const { isMobileMenuOpen, openMobileMenu, closeMobileMenu } = useMenuContext()

  // Get the current path for active state using Next.js hook
  const pathname = usePathname()

  // Function to check if a nav item is active
  const isNavItemActive = (itemHref: string) => {
    // Handle root path specially
    if (itemHref === "/" && pathname === "/") {
      return true;
    }

    // For other paths, check if the current path starts with the item href
    // This handles nested routes (e.g., /sport/football would match /sport)
    if (itemHref !== "/") {
      return pathname.startsWith(itemHref);
    }

    return false;
  }

  // Group the nav items into rows of 3
  const navItemRows = [
    navItems.slice(0, 3),
    navItems.slice(3, 6),
    navItems.slice(6, 9)
  ]

  return (
    <>
      <Button
        onClick={() => isMobileMenuOpen ? closeMobileMenu() : openMobileMenu()}
        variant="ghost"
        size="icon"
        className="md:hidden text-white p-0 focus:bg-transparent active:bg-transparent hover:bg-transparent"
      >
        <div className="w-6 h-6 flex items-center justify-center">
          <Image
            src={isMobileMenuOpen ? "/icons/close-icon.svg" : "/icons/sidebar-icon.svg"}
            alt={isMobileMenuOpen ? "Close menu" : "Open menu"}
            width={24}
            height={24}
            className="text-white"
            style={{ filter: 'brightness(1)' }}
          />
        </div>
        <span className="sr-only">Toggle menu</span>
      </Button>

      {isMobileMenuOpen && (
        <div
          className="fixed top-[60px] left-0 right-0 bottom-0 bg-black bg-opacity-50 z-30 md:hidden"
        />
      )}

      <div
        className={`fixed top-[60px] bottom-0 left-0 right-0 z-50 bg-[#222222] transition-all duration-300 ease-in-out md:hidden ${isMobileMenuOpen ? "translate-x-0" : "-translate-x-full"}`}
        style={{ height: 'calc(100% - 60px)', width: '100vw', paddingTop: '0px' }}
      >


        {navItemRows.map((row, rowIndex) => (
          <div key={`row-${rowIndex}`} className={`grid grid-cols-3 border-b border-black bg-black divide-x divide-black ${rowIndex === 0 ? 'border-t border-black' : ''}`}>
            {row.map((item) => (
              <NavItem
                key={item.name}
                icon={
                  item.icon ? (
                    <div className="w-6 h-6 flex items-center justify-center">
                      <Image src={item.icon} alt={item.name} width={24} height={24} className="text-white" />
                    </div>
                  ) : (
                    <div className="w-6 h-6 flex items-center justify-center">
                      <span className="rounded-full bg-yellow-500 w-2 h-2"></span>
                    </div>
                  )
                }
                label={item.name}
                href={item.href}
                onClick={() => closeMobileMenu()}
                isActive={isNavItemActive(item.href)}
              />
            ))}
          </div>
        ))}

      </div>
    </>
  )
}

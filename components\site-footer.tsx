"use client"

import Link from "next/link"
import Image from "next/image"
import { Shield } from "lucide-react"
import { useMobileView } from "@/hooks/use-mobile"

type SiteFooterProps = {}

export default function SiteFooter({}: SiteFooterProps) {
  const isMobile = useMobileView()
  return (
    <footer className="bg-black text-gray-400 border-t border-gray-800 text-center">
      {/* Main footer content - Hidden on mobile */}
      <div className={`container mx-auto px-4 py-6 flex flex-col items-center ${isMobile ? 'hidden' : ''}`}>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 w-full max-w-4xl">
          {/* Column 1: About */}
          <div className="mb-6 sm:mb-0 flex flex-col items-center">
            <h3 className="text-white font-bold mb-4">About</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/about" className="hover:text-yellow-500 transition-colors">
                  Who We Are
                </Link>
              </li>
              <li>
                <Link href="/contact" className="hover:text-yellow-500 transition-colors">
                  Contact
                </Link>
              </li>
              <li>
                <Link href="/careers" className="hover:text-yellow-500 transition-colors">
                  Careers
                </Link>
              </li>
              <li>
                <Link href="/affiliate" className="hover:text-yellow-500 transition-colors">
                  Affiliate Program
                </Link>
              </li>
            </ul>
          </div>

          {/* Column 2: Help */}
          <div className="mb-6 sm:mb-0 flex flex-col items-center">
            <h3 className="text-white font-bold mb-4">Help</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/faq" className="hover:text-yellow-500 transition-colors">
                  FAQ
                </Link>
              </li>
              <li>
                <Link href="/rules" className="hover:text-yellow-500 transition-colors">
                  Game Rules
                </Link>
              </li>
              <li>
                <Link href="/terms" className="hover:text-yellow-500 transition-colors">
                  Terms and Conditions
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="hover:text-yellow-500 transition-colors">
                  Privacy Policy
                </Link>
              </li>
            </ul>
          </div>

          {/* Column 3: Responsible Gaming */}
          <div className="mb-6 sm:mb-0 flex flex-col items-center">
            <h3 className="text-white font-bold mb-4">Responsible Gaming</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/responsible-gaming" className="hover:text-yellow-500 transition-colors">
                  Play Responsibly
                </Link>
              </li>
              <li>
                <Link href="/self-exclusion" className="hover:text-yellow-500 transition-colors">
                  Self-Exclusion
                </Link>
              </li>
              <li>
                <Link href="/protection" className="hover:text-yellow-500 transition-colors">
                  Minor Protection
                </Link>
              </li>
              <li>
                <div className="flex items-center mt-2">
                  <Shield className="h-5 w-5 mr-2 text-yellow-500" />
                  <span>18+ Gambling</span>
                </div>
              </li>
            </ul>
          </div>

          {/* Column 4: Social Media */}
          <div className="flex flex-col items-center">
            <h3 className="text-white font-bold mb-4">Follow Us</h3>
            <div className="flex space-x-4">
              <Link href="https://facebook.com" className="hover:text-yellow-500 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path></svg>
              </Link>
              <Link href="https://twitter.com" className="hover:text-yellow-500 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg>
              </Link>
              <Link href="https://instagram.com" className="hover:text-yellow-500 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5"><rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line></svg>
              </Link>
              <Link href="https://youtube.com" className="hover:text-yellow-500 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5"><path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"></path><polygon points="9.75 15.02 15.5 11.75 9.75 8.48 9.75 15.02"></polygon></svg>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom footer with payment methods and cookie text/social media */}
      <div className="border-t border-gray-800 py-4 w-full">
        <div className="container mx-auto px-4 text-center flex flex-col items-center">
          <div className="mb-4">
            <Image
              src="/footer-payments.png"
              alt="Payment Methods"
              width={500}
              height={50}
              className="mx-auto h-auto"
            />
          </div>

          {/* Desktop: Cookie text */}
          <p className={`text-sm max-w-3xl mx-auto ${isMobile ? 'hidden' : ''}`}>
            Nous utilisons des cookies et ceux des tiers pour améliorer nos services, analyser et personnaliser vos
            préférences et pour vous présenter des publicités. Si vous continuez de naviguer, nous considérons que vous
            acceptez de les utilisers. Vous pouvez modifier les paramètres et obtenir de plus amples informations à
            partir de notre politique relative aux cookies.
          </p>

          {/* Mobile: Social Media Links */}
          <div className={`${isMobile ? 'block' : 'hidden'} mt-4`}>
            <h3 className="text-white font-bold mb-4">Follow Us</h3>
            <div className="flex space-x-6 justify-center">
              <Link href="https://facebook.com" className="hover:text-yellow-500 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path></svg>
              </Link>
              <Link href="https://twitter.com" className="hover:text-yellow-500 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg>
              </Link>
              <Link href="https://instagram.com" className="hover:text-yellow-500 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6"><rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line></svg>
              </Link>
              <Link href="https://youtube.com" className="hover:text-yellow-500 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6"><path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"></path><polygon points="9.75 15.02 15.5 11.75 9.75 8.48 9.75 15.02"></polygon></svg>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import LoginForm from "@/components/login-form"
import Logo from "@/components/logo"
import { authUtils } from "@/utils/auth.utils"

export default function LoginPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Check if user is already logged in
    const userData = authUtils.getUserData()
    if (userData) {
      // Redirect to home page if already logged in
      router.push('/')
    } else {
      setLoading(false)
    }
  }, [])

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-black text-white">
        <p>Loading...</p>
      </div>
    )
  }

  return (
    <div className="flex flex-col bg-black text-white">
      {/* Header */}
      <div className="p-4 flex justify-center">
        <Link href="/">
          <Logo className="w-28 h-12" />
        </Link>
      </div>

      {/* Form directly under header */}
      <div className="p-4">
        <div className="w-full max-w-md mx-auto">
          <LoginForm />
        </div>
      </div>
    </div>
  )
}

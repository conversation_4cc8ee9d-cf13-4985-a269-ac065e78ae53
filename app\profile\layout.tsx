"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { NavItem } from "@/components/profile/nav-item"
import { authUtils } from "@/utils/auth.utils"

export default function ProfileLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const router = useRouter()

  // Check if user is logged in
  useEffect(() => {
    const userData = authUtils.getUserData()
    if (!userData) {
      router.push("/")
    }
  }, [router])

  return (
    <div className="min-h-screen bg-[#121212] text-gray-200">
      <div className="flex flex-col md:flex-row">
        {/* Sidebar - hidden on mobile */}
        <aside className="hidden md:block md:w-64 bg-[#121212] p-3 space-y-6">
          {/* User Account Settings section */}
          <section className="space-y-2">
            <h2 className="text-xs font-medium text-[#CAD5D5]">User Account Settings</h2>
            <div className="bg-[#1e1e1e] rounded overflow-hidden">
              <NavItem
                href="/profile/personal-details"
                label="Personal Details"
              />
              <NavItem
                href="/profile/change-password"
                label="Change Password"
                isLast={true}
              />
            </div>
          </section>

          {/* Player Activity section */}
          <section className="space-y-2">
            <h2 className="text-xs font-medium text-[#CAD5D5]">Player Activity</h2>
            <div className="bg-[#1e1e1e] rounded overflow-hidden">
              <NavItem
                href="/profile/betting-history"
                label="Betting History"
              />
              <NavItem
                href="/profile/transactions"
                label="Transactions"
                isLast={true}
              />
            </div>
          </section>
        </aside>

        {/* Main content */}
        <main className="w-full flex-1 bg-[#1e1e1e] p-4">{children}</main>
      </div>
    </div>
  )
}

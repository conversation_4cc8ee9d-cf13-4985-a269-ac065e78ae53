"use client"

import { useState, useEffect } from "react"
import { format, parseISO } from "date-fns"
import { CalendarIcon, X, ChevronLeft, ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { cashflowService, Transaction, TransactionHistoryParams } from "@/services/cashflow.service"
import { toast } from "sonner"

export default function TransactionsPage() {
  const [startDate, setStartDate] = useState<Date | undefined>(undefined)
  const [endDate, setEndDate] = useState<Date | undefined>(undefined)
  const [transactionType, setTransactionType] = useState("all")
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [allTransactions, setAllTransactions] = useState<Transaction[]>([]) // Store all fetched transactions
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [limit] = useState(10) // Number of transactions per page

  // Filter transactions when transaction type changes
  useEffect(() => {
    if (allTransactions.length > 0) {
      if (transactionType === "all") {
        setTransactions(allTransactions);
      } else {
        const filtered = allTransactions.filter(t => t.type === transactionType);
        setTransactions(filtered);
      }
    }
  }, [transactionType, allTransactions]);

  // Fetch transactions on initial load
  useEffect(() => {
    fetchTransactions()
  }, [])

  // Function to fetch transactions with current filters
  const fetchTransactions = async (page = currentPage) => {
    setLoading(true)
    setError(null)

    try {
      // Prepare parameters
      const params: TransactionHistoryParams = {
        page,
        limit,
      }

      // Add date filters if selected
      // We're using a special approach to handle dates to ensure that when a user selects
      // the same date for both start and end date (e.g., 21st for both), the entire day is included
      if (startDate) {
        // Get the year, month, and day directly from the Date object
        const year = startDate.getFullYear();
        const month = String(startDate.getMonth() + 1).padStart(2, '0');
        const day = String(startDate.getDate()).padStart(2, '0');

        // Create a properly formatted ISO string with time set to start of day
        params.startDate = `${year}-${month}-${day}T00:00:00.000Z`;

        // Log for debugging
        console.log('Start date:', params.startDate);
      }

      if (endDate) {
        // Get the year, month, and day directly from the Date object
        const year = endDate.getFullYear();
        const month = String(endDate.getMonth() + 1).padStart(2, '0');
        const day = String(endDate.getDate()).padStart(2, '0');

        // Create a properly formatted ISO string with time set to end of day
        params.endDate = `${year}-${month}-${day}T23:59:59.999Z`;

        // Log for debugging
        console.log('End date:', params.endDate);
      }

      // Note: We're not adding transaction type to the API params anymore
      // as we'll filter client-side

      // Call the API
      const response = await cashflowService.getTransactionHistory(params)

      // Store all transactions
      setAllTransactions(response.data)

      // Apply current filter
      if (transactionType === "all") {
        setTransactions(response.data)
      } else {
        const filtered = response.data.filter(t => t.type === transactionType);
        setTransactions(filtered)
      }

      setCurrentPage(response.pagination.page)
      setTotalPages(response.pagination.totalPages)
    } catch (err) {
      console.error("Error fetching transactions:", err)
      setError("Failed to load transactions. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage)
      fetchTransactions(newPage)
    }
  }

  // Handle filter button click
  const handleViewTransactions = () => {
    setCurrentPage(1) // Reset to first page when applying new filters
    fetchTransactions(1)
  }

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      // Check if the date is valid
      const date = parseISO(dateString)
      if (isNaN(date.getTime())) {
        return "Invalid date"
      }
      return format(date, "dd/MM/yyyy HH:mm")
    } catch (err) {
      console.error("Error formatting date:", err)
      return "Invalid date"
    }
  }

  // Format date for display in date picker
  const formatDateForDisplay = (date: Date) => {
    return format(date, "dd/MM/yyyy")
  }

  // Format amount
  const formatAmount = (amount: number | string) => {
    // Convert amount to number if it's a string
    const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

    // Check if it's a valid number
    if (isNaN(numericAmount)) {
      return "0.00";
    }

    return numericAmount.toFixed(2);
  }

  return (
    <div className="w-full p-4 bg-[#121212] text-white rounded-lg">
      <h1 className="text-base font-bold mb-3">Transaction History</h1>

      <div className="flex flex-col md:flex-row items-end gap-4 mb-4">
        {/* Date range section */}
        <div className="w-full">
          <div className="flex flex-col md:flex-row items-center gap-2">
            <div className="w-full md:w-auto flex-grow">
              <div className="flex items-center">
                <span className="hidden md:inline-block text-xs mr-2">Date range</span>
                {startDate && endDate && (
                  <div className="hidden md:flex items-center">
                    <span className="text-xs mr-2 text-yellow-400">
                      {formatDateForDisplay(startDate)} - {formatDateForDisplay(endDate)}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setStartDate(undefined);
                        setEndDate(undefined);
                      }}
                      className="h-6 px-2 text-xs text-gray-400 hover:text-white"
                    >
                      Clear
                    </Button>
                  </div>
                )}
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full md:w-[250px] flex items-center text-xs font-normal bg-[#313131] border-[#4F4F4F] hover:bg-[#252525] hover:text-white h-8",
                      )}
                    >
                      <span className="flex-1 text-left">{startDate ? formatDateForDisplay(startDate) : "Start date"}</span>
                      <CalendarIcon className="ml-2 h-3 w-3" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0 bg-[#313131] border-[#4F4F4F]">
                    <Calendar
                      mode="single"
                      selected={startDate}
                      onSelect={setStartDate}
                      initialFocus
                      className="bg-[#313131] text-white"
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="w-full md:w-auto flex-grow mt-2 md:mt-0 md:ml-4">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full md:w-[250px] flex items-center text-xs font-normal bg-[#313131] border-[#4F4F4F] hover:bg-[#252525] hover:text-white h-8",
                    )}
                  >
                    <span className="flex-1 text-left">{endDate ? formatDateForDisplay(endDate) : "End date"}</span>
                    <CalendarIcon className="ml-2 h-3 w-3" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 bg-[#313131] border-[#4F4F4F]">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={setEndDate}
                    initialFocus
                    className="bg-[#313131] text-white"
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>

        {/* Transaction type section */}
        <div className="w-full mt-4 md:mt-0">
          <div className="flex items-center">
            <span className="hidden md:inline-block text-xs mr-2">Transaction type</span>
            <div className="w-full md:w-auto flex-grow">
              <Select
                value={transactionType}
                onValueChange={(value) => {
                  setTransactionType(value);
                  // No need to call fetchTransactions here as the useEffect will handle filtering
                }}
              >
                <SelectTrigger className="w-full md:w-[250px] bg-[#313131] border-[#4F4F4F] text-white h-8 text-xs">
                  <SelectValue placeholder="All transactions" />
                </SelectTrigger>
                <SelectContent className="bg-[#313131] border-[#4F4F4F] text-white">
                  <SelectItem value="all" className="text-xs">All transactions</SelectItem>
                  <SelectItem value="add" className="text-xs">Deposit</SelectItem>
                  <SelectItem value="deduct" className="text-xs">Withdraw</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* View button section */}
        <div className="w-full mt-4 md:mt-0 md:flex md:justify-end">
          <Button
            onClick={handleViewTransactions}
            className="bg-emerald-600 hover:bg-emerald-700 text-white w-full md:w-auto px-6 h-8 text-xs"
            disabled={loading}
          >
            {loading ? "Loading..." : "View"}
          </Button>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="mt-4 mb-4 p-3 bg-red-900/20 border border-red-900/50 rounded-md text-red-400 text-xs">
          {error}
        </div>
      )}

      {/* Loading indicator */}
      {loading && !error && (
        <div className="mt-4 mb-4 text-center py-2">
          <div className="inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent motion-reduce:animate-[spin_1.5s_linear_infinite] text-emerald-500"></div>
          <span className="ml-2 text-xs text-gray-300">Loading transactions...</span>
        </div>
      )}

      <div className="border border-[#4F4F4F] rounded-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full min-w-[800px]">
            <thead>
              <tr className="bg-[#252525]">
                <th className="px-3 py-2 text-left text-xs font-medium text-gray-300">Amount</th>
                <th className="px-3 py-2 text-left text-xs font-medium text-gray-300">From</th>
                <th className="px-3 py-2 text-left text-xs font-medium text-gray-300">Type</th>
                <th className="px-3 py-2 text-left text-xs font-medium text-gray-300">To</th>
                <th className="px-3 py-2 text-left text-xs font-medium text-gray-300">Date</th>
              </tr>
            </thead>
            <tbody>
              {transactions.length > 0 && transactions.map((transaction) => (
                <tr key={transaction.id} className="border-t border-[#4F4F4F]">
                  <td className="px-3 py-2 text-xs text-gray-300">
                    <span className={
                      transaction.type === 'add' ? 'text-green-500' : 'text-red-500'
                    }>
                      {formatAmount(transaction.amount)} TND
                    </span>
                  </td>
                  <td className="px-3 py-2 text-xs text-gray-300">
                    <div className="flex flex-col">
                      <span>{transaction.fromUser.username}</span>
                      <span className="text-gray-500 text-[10px]">{transaction.fromUser.role}</span>
                    </div>
                  </td>
                  <td className="px-3 py-2 text-xs">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      transaction.type === 'add' ? 'bg-green-900/20 text-green-500' :
                      transaction.type === 'deduct' ? 'bg-red-900/20 text-red-500' :
                      'bg-gray-900/20 text-gray-500'
                    }`}>
                      {transaction.type === 'add' ? 'Deposit' :
                       transaction.type === 'deduct' ? 'Withdraw' :
                       transaction.type}
                    </span>
                  </td>
                  <td className="px-3 py-2 text-xs text-gray-300">
                    <div className="flex flex-col">
                      <span>{transaction.toUser.username}</span>
                      <span className="text-gray-500 text-[10px]">{transaction.toUser.role}</span>
                    </div>
                  </td>
                  <td className="px-3 py-2 text-xs text-gray-300">{formatDate(transaction.createdAt)}</td>
                </tr>
              ))}
            </tbody>
          </table>

          {/* Empty state */}
          {!loading && transactions.length === 0 && (
            <div className="flex flex-col items-center justify-center py-8">
              <div className="w-12 h-12 rounded-full border-2 border-[#4F4F4F] flex items-center justify-center mb-3">
                <X className="w-6 h-6 text-[#4F4F4F]" />
              </div>
              <p className="text-gray-300 text-xs">No transactions found</p>
            </div>
          )}

          {/* Pagination controls */}
          {transactions.length > 0 && totalPages > 1 && (
            <div className="flex items-center justify-between px-4 py-3 bg-[#1e1e1e] border-t border-[#4F4F4F]">
              <div className="text-xs text-gray-400">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1 || loading}
                  className="h-8 px-2 bg-[#313131] border-[#4F4F4F] hover:bg-[#252525]"
                >
                  <ChevronLeft className="h-4 w-4" />
                  <span className="sr-only">Previous page</span>
                </Button>

                {/* Numbered pagination */}
                <div className="flex space-x-1">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <Button
                      key={page}
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(page)}
                      disabled={loading}
                      className={`h-8 w-8 flex items-center justify-center text-xs ${
                        currentPage === page
                          ? 'bg-[#FCD301] text-black border-[#FCD301] hover:bg-[#FCD301]'
                          : 'bg-[#181818] text-white border-[#4F4F4F] hover:bg-[#252525]'
                      }`}
                    >
                      {page}
                    </Button>
                  ))}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages || loading}
                  className="h-8 px-2 bg-[#313131] border-[#4F4F4F] hover:bg-[#252525]"
                >
                  <ChevronRight className="h-4 w-4" />
                  <span className="sr-only">Next page</span>
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

import React from 'react';

const Logo = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="533"
    height="225"
    viewBox="0 0 533 225"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="header-block-mobile__logo"
    {...props} // allows passing className, etc.
  >
    <g filter="url(#filter0_d_4_47)"><path d="M95.2309 113.151V192.79H78.0614V182.5C75.0797 186.278 71.3764 189.111 66.9517 191C62.6233 192.89 57.8139 193.834 52.5235 193.834C45.0208 193.834 38.2877 192.144 32.3241 188.763C26.4565 185.383 21.8395 180.611 18.473 174.446C15.2026 168.282 13.5674 161.123 13.5674 152.97C13.5674 144.818 15.2026 137.709 18.473 131.644C21.8395 125.48 26.4565 120.707 32.3241 117.327C38.2877 113.946 45.0208 112.256 52.5235 112.256C57.5253 112.256 62.0942 113.151 66.2303 114.941C70.4625 116.73 74.1177 119.365 77.1958 122.845V113.151H95.2309ZM54.5435 177.876C61.1804 177.876 66.6631 175.59 70.9916 171.016C75.32 166.443 77.4844 160.427 77.4844 152.97C77.4844 145.514 75.32 139.499 70.9916 134.925C66.6631 130.351 61.1804 128.065 54.5435 128.065C47.9065 128.065 42.4238 130.351 38.0953 134.925C33.8631 139.499 31.7469 145.514 31.7469 152.97C31.7469 160.427 33.8631 166.443 38.0953 171.016C42.4238 175.59 47.9065 177.876 54.5435 177.876Z" fill="white"></path><path d="M123.333 101.647C116.296 101.647 117.189 100.424 117.189 108.975V113.747H138.398V128.661H117.622V192.79H99.5864V108.677C99.5864 100.226 101.991 93.5143 106.801 88.5432C111.178 84.0185 115.562 84.8497 124.411 84.8497C131.015 84.8497 130.939 84.8506 138.011 84.8502V101.647C134.622 101.647 126.988 101.647 123.333 101.647Z" fill="white"></path><path d="M168.116 113.151C182.354 113.151 171.072 113.151 185.119 113.151V129.556C183.58 129.258 182.185 129.556 180.935 129.556C173.913 129.556 168.43 131.693 164.487 135.969C160.543 140.145 158.571 146.209 158.571 154.164V192.79H140.536C140.536 192.79 139.828 167.342 140.536 145.206C140.536 130.898 150.505 113.151 168.116 113.151Z" fill="white"></path><path d="M257.326 193.834C249.343 193.834 242.176 192.094 235.828 188.614C229.479 185.134 224.527 180.312 220.966 174.148C217.408 167.884 215.628 160.825 215.628 152.97C215.628 145.116 217.408 138.106 220.966 131.942C224.527 125.778 229.432 120.956 235.683 117.476C242.032 113.996 249.246 112.256 257.326 112.256C264.924 112.256 271.562 113.847 277.237 117.028C283.007 120.21 287.337 124.784 290.221 130.749L276.371 139.101C274.16 135.422 271.369 132.688 268.002 130.898C264.732 129.009 261.125 128.065 257.181 128.065C250.448 128.065 244.87 130.351 240.445 134.925C236.02 139.399 233.809 145.414 233.809 152.97C233.809 160.527 235.972 166.592 240.301 171.165C244.724 175.639 250.353 177.876 257.181 177.876C261.125 177.876 264.732 176.982 268.002 175.192C271.369 173.303 274.16 170.519 276.371 166.84L290.221 175.192C287.24 181.158 282.864 185.781 277.092 189.062C271.417 192.243 264.829 193.834 257.326 193.834Z" fill="white"></path><path d="M369.9 113.151V192.79H352.732V182.5C349.749 186.278 346.047 189.111 341.622 191C337.292 192.89 332.484 193.834 327.194 193.834C319.692 193.834 312.958 192.144 306.995 188.763C301.126 185.383 296.509 180.611 293.142 174.446C289.873 168.282 288.237 161.123 288.237 152.97C288.237 144.818 289.873 137.709 293.142 131.644C296.509 125.48 301.126 120.707 306.995 117.327C312.958 113.946 319.692 112.256 327.194 112.256C332.195 112.256 336.765 113.151 340.901 114.941C345.132 116.73 348.788 119.365 351.865 122.845V113.151H369.9ZM329.214 177.876C335.85 177.876 341.333 175.59 345.661 171.016C349.989 166.443 352.154 160.427 352.154 152.97C352.154 145.514 349.989 139.499 345.661 134.925C341.333 130.351 335.85 128.065 329.214 128.065C322.576 128.065 317.094 130.351 312.765 134.925C308.533 139.499 306.417 145.514 306.417 152.97C306.417 160.427 308.533 166.443 312.765 171.016C317.094 175.59 322.576 177.876 329.214 177.876Z" fill="white"></path><path d="M192.286 113.747H210.322V193.387H192.286V113.747Z" fill="white"></path><path d="M497.076 97.1781V189.902H483.974V108.702H463.061V97.1781H497.076Z" fill="#FFE628"></path><path fillRule="evenodd" clipRule="evenodd" d="M501.487 92.7625V194.317H479.56V113.118H458.647V92.7625H501.487ZM483.972 108.702V189.901H497.076V97.1779H463.059V108.702H483.972Z" fill="black"></path><path fillRule="evenodd" clipRule="evenodd" d="M404.442 92.7625L454.771 184.802L431.449 194.076L391.206 120.485L381.12 102.037L404.442 92.7625ZM393.712 114.617L433.953 188.208L447.89 182.666L401.938 98.6303L388.001 104.172L393.712 114.617Z" fill="black"></path><path fillRule="evenodd" clipRule="evenodd" d="M454.771 102.037L404.442 194.076L381.12 184.802L421.361 111.21L431.449 92.7625L454.771 102.037ZM428.241 109.075L388.001 182.666L401.938 188.208L447.89 104.172L433.953 98.6303L428.241 109.075Z" fill="black"></path><path d="M448.377 104.266L402.237 188.646L387.943 182.962L428.35 109.069L434.083 98.5815L448.377 104.266Z" fill="#FFE628"></path><path d="M402.237 98.5815L448.377 182.962L434.083 188.646L393.678 114.753L387.943 104.266L402.237 98.5815Z" fill="#FFE628"></path><path d="M204.321 76.0124H205.195C204.321 76.0124 204.321 76.0128 204.321 76.0134V76.0146V76.0173V76.0246V76.0479C204.321 76.0669 204.323 76.0931 204.323 76.1265C204.325 76.193 204.329 76.2879 204.337 76.4101C204.354 76.6545 204.385 77.0082 204.445 77.4641C204.567 78.3757 204.808 79.6954 205.285 81.3641C206.159 84.4317 207.829 88.6728 211.016 93.7248H192.081C195.723 88.6759 197.39 84.4234 198.133 81.3281C198.538 79.64 198.666 78.3014 198.691 77.3717C198.702 76.907 198.689 76.5451 198.671 76.2929C198.664 76.1668 198.654 76.0683 198.646 75.9983C198.642 75.9632 198.639 75.9352 198.635 75.9147L198.631 75.8889V75.8801L198.629 75.8768V75.8755C198.629 75.8749 198.629 75.8743 197.766 76.0124L198.629 75.8743L198.359 74.1824L197.148 75.3939C193.705 78.8395 190.324 78.8552 186.622 78.8552C183.192 78.8552 179.689 77.1299 177.025 74.4646C174.362 71.7993 172.638 68.2932 172.638 64.8602C172.638 55.91 177.086 49.6153 183.084 43.151C184.462 41.6654 185.926 40.169 187.431 38.6298C192.15 33.8042 197.28 28.5599 201.48 21.918C205.682 28.5599 210.812 33.8042 215.53 38.6298C217.036 40.169 218.499 41.6654 219.877 43.151C225.876 49.6153 230.324 55.91 230.324 64.8602C230.324 68.2932 228.599 71.7993 225.936 74.4646C223.273 77.1299 219.769 78.8552 216.34 78.8552C212.638 78.8552 209.256 78.8395 205.814 75.3939L204.321 73.9007V76.0124Z" fill="#FFE631" stroke="black" strokeWidth="0.901896"></path></g><defs><filter id="filter0_d_4_47" x="7.2541" y="14.7611" width="509.565" height="194.888" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB"><feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood><feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></feColorMatrix><feOffset dx="4.50948" dy="4.50948"></feOffset><feGaussianBlur stdDeviation="5.41138"></feGaussianBlur><feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.7 0"></feColorMatrix><feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4_47"></feBlend><feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_4_47" result="shape"></feBlend></filter></defs>
  </svg>
);

export default Logo;

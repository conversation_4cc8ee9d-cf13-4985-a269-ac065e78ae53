'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { authUtils } from '@/utils/auth.utils';

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check if we're on a public page that doesn't require authentication
        const isPublicPage = window.location.pathname === '/' ||
                            window.location.pathname === '/en' ||
                            window.location.pathname === '/fr' ||
                            window.location.pathname.includes('/games') ||
                            window.location.pathname.includes('/about') ||
                            window.location.pathname.includes('/contact');

        // Try to get user data from localStorage
        const userData = authUtils.getUserData();

        if (userData) {
          // User is logged in, set up token refresh
          await authUtils.setupTokenRefresh();

          if (isPublicPage) {
            setIsLoading(false);
            return;
          }

          // Ensure we have a valid token for protected pages
          try {
            const isValid = await authUtils.ensureValidToken();

            if (!isValid) {
              router.push('/');
              return;
            }

            setIsLoading(false);
          } catch (error) {
            // In development, we'll continue anyway to help with debugging
            if (process.env.NODE_ENV === 'development') {
              console.log(error);
              setIsLoading(false);
            } else {
              // In production, we'll clear auth data and redirect to login
              authUtils.clearAuthData();
              router.push('/');
              return;
            }
          }
        } else {
          // No user data
          if (isPublicPage) {
            setIsLoading(false);
            return;
          } else {
            // Redirect to login for protected pages
            router.push('/');
            return;
          }
        }
      } catch (error) {
        console.log(error);
        router.push('/');
      }
    };

    checkAuth();
  }, [router]);

  if (isLoading) {
    // Return loading indicator or null
    return null;
  }

  return <>{children}</>;
}

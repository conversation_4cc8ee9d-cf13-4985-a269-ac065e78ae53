"use client"

import * as React from "react"
import * as SheetPrimitive from "@radix-ui/react-dialog"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const CustomSheet = SheetPrimitive.Root

const CustomSheetTrigger = SheetPrimitive.Trigger

const CustomSheetClose = SheetPrimitive.Close

const CustomSheetPortal = SheetPrimitive.Portal

// Custom overlay that starts below the header
const CustomSheetOverlay = React.forwardRef<
  React.ElementRef<typeof SheetPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay> & { headerHeight?: number }
>(({ className, headerHeight = 60, ...props }, ref) => (
  <SheetPrimitive.Overlay
    className={cn(
      "fixed inset-0 z-50 bg-black/60 transition-opacity duration-300 ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
      className
    )}
    style={{ top: `${headerHeight}px` }}
    {...props}
    ref={ref}
  />
))
CustomSheetOverlay.displayName = "CustomSheetOverlay"

const customSheetVariants = cva(
  "fixed z-50 gap-4 bg-background p-6 shadow-lg transition-all duration-300 ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",
  {
    variants: {
      side: {
        top: "inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",
        bottom:
          "inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",
        left: "inset-y-0 left-0 h-full w-full border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:w-[350px]",
        right:
          "inset-y-0 right-0 h-full w-full border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:w-[350px]",
      },
    },
    defaultVariants: {
      side: "right",
    },
  }
)

interface CustomSheetContentProps
  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,
    VariantProps<typeof customSheetVariants> {
  headerHeight?: number;
}

const CustomSheetContent = React.forwardRef<
  React.ElementRef<typeof SheetPrimitive.Content>,
  CustomSheetContentProps
>(({ side = "right", headerHeight = 60, className, children, ...props }, ref) => (
  <CustomSheetPortal>
    <CustomSheetOverlay headerHeight={headerHeight} />
    <SheetPrimitive.Content
      ref={ref}
      className={cn(customSheetVariants({ side }), className)}
      style={{
        top: `${headerHeight}px`,
        height: `calc(100% - ${headerHeight}px)`
      }}
      {...props}
    >
      <SheetPrimitive.Title className="sr-only">
        User Menu
      </SheetPrimitive.Title>
      {children}
    </SheetPrimitive.Content>
  </CustomSheetPortal>
))
CustomSheetContent.displayName = "CustomSheetContent"

export {
  CustomSheet,
  CustomSheetTrigger,
  CustomSheetClose,
  CustomSheetContent,
}

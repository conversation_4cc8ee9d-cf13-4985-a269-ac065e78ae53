import Link from "next/link"
import GameCarousel from "@/components/game-carousel"

export default function Home() {
  return (
    <>

      {/* Sport categories for mobile */}
      <div className="md:hidden px-2 py-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-white font-bold">Sport</h2>
          <span className="text-yellow-500 font-bold">Paris Sportifs</span>
        </div>

        <div className="grid grid-cols-3 gap-2">
          <Link href="/mobile" className="relative aspect-square overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent z-10"></div>
            <img src="/modern-sports-betting-app.png" alt="Mobile" className="w-full h-full object-cover" />
            <span className="absolute bottom-2 left-0 right-0 text-center text-white text-xs z-20">Mobile</span>
          </Link>

          <Link href="/football" className="relative aspect-square overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent z-10"></div>
            <img src="/grass-soccer-ball.png" alt="Football" className="w-full h-full object-cover" />
            <span className="absolute bottom-2 left-0 right-0 text-center text-white text-xs z-20">Football</span>
          </Link>

          <Link href="/tennis" className="relative aspect-square overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent z-10"></div>
            <img src="/tennis-close-up.png" alt="Tennis" className="w-full h-full object-cover" />
            <span className="absolute bottom-2 left-0 right-0 text-center text-white text-xs z-20">Tennis</span>
          </Link>
        </div>
      </div>

      {/* Game Carousel - Only visible on desktop */}
      <div className="hidden md:block py-4 md:py-8 px-2 md:px-4">
        <GameCarousel />
      </div>

    </>
  )
}

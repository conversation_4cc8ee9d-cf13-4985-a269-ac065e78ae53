"use client"

import { useState, ReactNode } from "react"
import Image from "next/image"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Button } from "@/components/ui/button"
// Custom implementation without using CustomSheet
import { useMenuContext } from "@/contexts/menu-context"
import { LoginDialog } from "@/components/login-dialog"
import LogoutButton from "@/components/LogoutButton"
import { useBalanceCheck } from "@/hooks/useBalanceCheck"
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from "@/components/ui/dropdown-menu"

// SidebarItem component
interface SidebarItemProps {
  label: string;
  icon?: ReactNode;
  href?: string;
  onClick?: () => void;
}

function SidebarItem({ label, icon, href, onClick }: SidebarItemProps) {
  const pathname = href ? usePathname() : null;
  const isActive = pathname === href;

  const content = (
    <div
      className={`flex items-center justify-between px-3 py-2 cursor-pointer
        ${icon ? 'bg-[#4f4f4f]' : 'bg-[#2f3031] border-b border-gray-700'}
        ${isActive ? 'bg-[#3a3b3c] border-l-4 border-l-[#FFCC00]' : 'hover:bg-[#3a3b3c]'}`}
      onClick={onClick}
    >
      <span className={`${icon ? 'text-sm font-bold' : 'text-sm'} ${isActive ? 'text-[#FFCC00]' : ''}`}>{label}</span>
      {icon && <div>{icon}</div>}
    </div>
  );

  if (href) {
    return <Link href={href}>{content}</Link>;
  }

  return content;
}

export default function UserSidebar() {
  const [language, setLanguage] = useState("English")
  const { isUserSidebarOpen, openUserSidebar, closeUserSidebar } = useMenuContext()

  // Use the balance check hook to get and periodically update user data
  const { userData: user } = useBalanceCheck()

  return (
    <>
      <div className="flex items-center">
        {user && !isUserSidebarOpen && (
          <div className="mr-2 text-white text-xs">
            <span>{user.currency}. {typeof user.balance === 'number' ? user.balance.toFixed(2) : user.balance}</span>
          </div>
        )}
        <Button
          onClick={() => isUserSidebarOpen ? closeUserSidebar() : openUserSidebar()}
          variant="ghost"
          size="icon"
          className="md:hidden text-white p-0 focus:bg-transparent active:bg-transparent hover:bg-transparent"
        >
          <div className="w-6 h-6 flex items-center justify-center">
            <Image
              src={isUserSidebarOpen ? "/icons/close-icon.svg" : "/icons/avatar-icon.svg"}
              alt={isUserSidebarOpen ? "Close menu" : "User menu"}
              width={24}
              height={24}
              className="text-white"
              style={{ filter: 'brightness(1)' }}
            />
          </div>
          <span className="sr-only">User menu</span>
        </Button>
      </div>

      {isUserSidebarOpen && (
        <div
          className="fixed top-[60px] left-0 right-0 bottom-0 bg-black bg-opacity-50 z-30 md:hidden"
        />
      )}

      <div
        className={`fixed top-[60px] bottom-0 right-0 z-50 w-full sm:w-[350px] transition-all duration-300 ease-in-out md:hidden flex flex-col ${isUserSidebarOpen ? "translate-x-0" : "translate-x-full"}`}
        style={{
          height: 'calc(100% - 60px)',
          borderLeft: '1px solid #2f3031',
          backgroundColor: '#2f3031',
          paddingTop: '0px' // Adjusted for fixed header
        }}
      >
        {user ? (
          // Logged in user view
          <div className="w-full h-full bg-[#2f3031] text-white overflow-y-auto">
            {/* User info section */}
            <div className="p-3 pb-4 border-b border-[#2f3031]">
              <div className="font-bold text-base">{user.username}</div>
              <div className="text-xs text-gray-300">Équilibre: {user.balance} {user.currency}</div>
            </div>

            {/* Menu items */}
            <div className="border-b border-[#2f3031]">
              <SidebarItem
                label="Activités du joueur"
                icon={<Image src="/icons/avatar-icon.svg" alt="User" width={18} height={18} />}
              />
            </div>

            <div className="border-b border-[#2f3031]">
              <SidebarItem
                label="Historique des paris"
                href="/profile/betting-history"
                onClick={closeUserSidebar}
              />
              <SidebarItem
                label="Transactions"
                href="/profile/transactions"
                onClick={closeUserSidebar}
              />
            </div>

            <div className="border-b border-[#2f3031]">
              <SidebarItem
                label="Paramètres du compte utilisateur"
                icon={<Image src="/icons/settings-icon.svg" alt="Settings" width={18} height={18} />}
              />
              <SidebarItem
                label="Paramètres des bookmakers"
                href=""
                onClick={closeUserSidebar}
              />
              <SidebarItem
                label="Détails personnels"
                href="/profile/personal-details"
                onClick={closeUserSidebar}
              />
              <SidebarItem
                label="Changer votre mot de passe"
                href="/profile/change-password"
                onClick={closeUserSidebar}
              />
            </div>

            <div className="border-b border-[#2f3031]">
              <SidebarItem
                label="Me déconnecter"
                icon={<Image src="/icons/exit-icon.svg" alt="Logout" width={18} height={18} />}
                onClick={() => {
                  closeUserSidebar();
                  const logoutBtn = document.querySelector('[data-logout-button]') as HTMLButtonElement;
                  if (logoutBtn) logoutBtn.click();
                }}
              />
            </div>
          </div>
        ) : (
          // Not logged in view
          <div className="p-3 mt-2">
            <LoginDialog>
              <Button className="w-full bg-black text-white hover:bg-gray-900 mb-4 py-4 font-bold" onClick={closeUserSidebar}>
                LOGIN
              </Button>
            </LoginDialog>

            <div className="flex flex-col gap-4">
              <div className="flex justify-center">
                <DropdownMenu>
                  <DropdownMenuTrigger className="bg-gray-800 text-white px-8 py-3 rounded text-sm">
                    {language}
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="center" className="bg-gray-800 text-white border-gray-700">
                    <DropdownMenuRadioGroup value={language} onValueChange={setLanguage}>
                      <DropdownMenuRadioItem value="English" className="focus:bg-gray-700">
                        English
                      </DropdownMenuRadioItem>
                      <DropdownMenuRadioItem value="Français" className="focus:bg-gray-700">
                        Français
                      </DropdownMenuRadioItem>
                    </DropdownMenuRadioGroup>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  )
}

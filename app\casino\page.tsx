"use client"

import { useState, useRef, useEffect } from "react"
import { useInView } from "react-intersection-observer"
import { Heart, Search, ChevronDown, Check } from "lucide-react"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useCategories, useTitles, useInfiniteGameSearch } from "@/hooks"
import { useFavorites, useIsFavorite, useToggleFavorite } from "@/hooks/useFavorites"
import { useDebounce } from "use-debounce"
import { Game } from "@/types/game.types"
import { gameService } from "@/services/game.service"

// Legacy interface for backward compatibility
interface LegacyGame {
  id: number
  title: string
  image: string
}

// Fallback games for when API is loading or fails
const fallbackGames: LegacyGame[] = [
  {
    id: 1,
    title: "Crazy Pachinko",
    image: "/vibrant-casino-night.png",
  },
  {
    id: 2,
    title: "MONOPOLY Live",
    image: "/casino-wheel-game.png",
  },
  {
    id: 3,
    title: "Game Shows",
    image: "/glamorous-casino-night.png",
  },
  {
    id: 4,
    title: "Crazy Time",
    image: "/colorful-wheel-game.png",
  },
]

// Hook to detect mobile
function useIsMobile() {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    // Initial check
    checkIfMobile()

    // Add event listener
    window.addEventListener("resize", checkIfMobile)

    // Cleanup
    return () => window.removeEventListener("resize", checkIfMobile)
  }, [])

  return isMobile
}

function DesktopGameCard({ game }: { game: Game | LegacyGame }) {
  const isLegacyGame = 'image' in game;
  const gameImage = isLegacyGame ? (game as LegacyGame).image : (game as Game).img;
  const gameTitle = isLegacyGame ? (game as LegacyGame).title : (game as Game).name;
  const gameId = isLegacyGame ? (game as LegacyGame).id.toString() : (game as Game).id;

  // Favorites functionality - only for real games, not legacy fallback games
  const isFavorite = useIsFavorite(gameId);
  const { toggleFavorite, isLoading: favoriteLoading } = useToggleFavorite();

  const handleGameClick = async () => {
    if (!isLegacyGame) {
      try {
        await gameService.launchGame(gameId);
      } catch (error) {
        console.error('Failed to launch game:', error);
      }
    }
  };

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isLegacyGame) {
      toggleFavorite(gameId, isFavorite);
    }
  };

  return (
    <div className="relative group cursor-pointer" onClick={handleGameClick}>
      <div className="absolute inset-0 border-[4px] border-transparent group-hover:border-[#FCD301] transition-colors duration-200 pointer-events-none z-10"></div>
      <div className="relative aspect-[16/9] overflow-hidden">
        <Image src={gameImage || "/placeholder.svg"} alt={gameTitle} fill className="object-cover" />
        <div className="absolute inset-0 bg-[#FCD301]/0 group-hover:bg-[#FCD301]/40 transition-colors duration-200"></div>

        {/* Play Button */}
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10">
          <div className="w-12 h-12 rounded-full bg-white flex items-center justify-center">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M8 5.14v14.72a1 1 0 0 0 1.5.86l11-7.36a1 1 0 0 0 0-1.72l-11-7.36a1 1 0 0 0-1.5.86z"
                fill="#FCD301"
              />
            </svg>
          </div>
        </div>
      </div>
      <div className="py-2 px-2 bg-transparent group-hover:bg-[#FCD301] transition-colors duration-200 flex justify-between items-center">
        <h3 className="text-sm font-medium text-white group-hover:text-black transition-colors duration-200">
          {gameTitle}
        </h3>
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6 p-0 bg-transparent hover:bg-transparent"
          onClick={handleFavoriteClick}
          disabled={favoriteLoading || isLegacyGame}
        >
          <Heart className={`h-4 w-4 transition-colors duration-200 ${
            !isLegacyGame && isFavorite
              ? "text-red-500 fill-red-500"
              : "text-white group-hover:text-black"
          }`} />
          <span className="sr-only">
            {!isLegacyGame && isFavorite ? "Remove from favorites" : "Add to favorites"}
          </span>
        </Button>
      </div>
    </div>
  )
}

function MobileGameCard({ game }: { game: Game | LegacyGame }) {
  const isLegacyGame = 'image' in game;
  const gameImage = isLegacyGame ? (game as LegacyGame).image : (game as Game).img;
  const gameTitle = isLegacyGame ? (game as LegacyGame).title : (game as Game).name;
  const gameId = isLegacyGame ? (game as LegacyGame).id.toString() : (game as Game).id;

  // Favorites functionality - only for real games, not legacy fallback games
  const isFavorite = useIsFavorite(gameId);
  const { toggleFavorite, isLoading: favoriteLoading } = useToggleFavorite();

  const handleGameClick = async () => {
    if (!isLegacyGame) {
      try {
        await gameService.launchGame(gameId);
      } catch (error) {
        console.error('Failed to launch game:', error);
      }
    }
  };

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isLegacyGame) {
      toggleFavorite(gameId, isFavorite);
    }
  };

  return (
    <div className="relative cursor-pointer" onClick={handleGameClick}>
      <div className="relative aspect-[16/9] overflow-hidden">
        <Image src={gameImage || "/placeholder.svg"} alt={gameTitle} fill className="object-cover" />

        {/* Play Button */}
        <div className="absolute inset-0 flex items-center justify-center z-10">
          <div className="w-8 h-8 rounded-full bg-[#FCD301] flex items-center justify-center">
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M8 5.14v14.72a1 1 0 0 0 1.5.86l11-7.36a1 1 0 0 0 0-1.72l-11-7.36a1 1 0 0 0-1.5.86z"
                fill="white"
              />
            </svg>
          </div>
        </div>
      </div>

      <div className="bg-[#FCD301] py-1 px-2 flex justify-between items-center">
        <h3 className="text-[10px] font-medium text-black truncate pr-2">{gameTitle}</h3>
        <button
          onClick={handleFavoriteClick}
          disabled={favoriteLoading || isLegacyGame}
          className="p-0.5 hover:bg-black/10 rounded transition-colors"
        >
          <Heart className={`h-3 w-3 transition-colors ${
            !isLegacyGame && isFavorite
              ? "text-red-600 fill-red-600"
              : "text-black"
          }`} />
        </button>
      </div>
    </div>
  )
}

export default function GamingInterface() {
  const [activeCategory, setActiveCategory] = useState("All")
  const [showFavorites, setShowFavorites] = useState(false)
  const [searchOpen, setSearchOpen] = useState(false)
  const [showProviderDropdown, setShowProviderDropdown] = useState(false)
  const [selectedProvider, setSelectedProvider] = useState("ALL")
  const [searchTerm, setSearchTerm] = useState("")
  const [desktopSearchTerm, setDesktopSearchTerm] = useState("")
  const [activeSearchTerm, setActiveSearchTerm] = useState("")
  const searchInputRef = useRef<HTMLInputElement>(null)
  const categoryScrollRef = useRef<HTMLDivElement>(null)
  const providerDropdownRef = useRef<HTMLDivElement>(null)
  const isMobile = useIsMobile()

  // API hooks
  const { data: categories } = useCategories()
  const { data: titles } = useTitles()
  const { data: favoritesData, isLoading: favoritesLoading } = useFavorites()

  // Search logic - different for desktop and mobile
  const [debouncedMobileSearch] = useDebounce(searchTerm, 500) // 500ms debounce for mobile

  // Determine which search term to use based on device
  const finalSearchTerm = isMobile
    ? (debouncedMobileSearch && debouncedMobileSearch.length >= 3 ? debouncedMobileSearch : undefined)
    : activeSearchTerm || undefined

  // Desktop search function
  const handleDesktopSearchKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      setActiveSearchTerm(desktopSearchTerm)
    }
  }

  // Infinite scroll setup
  const { ref: loadMoreRef, inView } = useInView({
    threshold: 0.1,
    triggerOnce: false,
  })

  // Game search with infinite scroll - use original values
  const searchFilters = {
    name: finalSearchTerm,
    categories: activeCategory !== "All" ? [activeCategory] : undefined,
    title: selectedProvider !== "ALL" ? selectedProvider : undefined,
  }

  const {
    data: infiniteGameData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: gamesLoading,
    error: gamesError
  } = useInfiniteGameSearch(searchFilters)

  // Trigger load more when in view - but not when showing favorites
  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage && !showFavorites) {
      fetchNextPage()
    }
  }, [inView, hasNextPage, isFetchingNextPage, fetchNextPage, showFavorites])

  // Helper function to format text: capitalize first letter and handle underscores
  const formatText = (text: string): string => {
    return text
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  // Use API data or fallback to hardcoded values - keep original values
  const displayCategories = categories
    ? ["All", ...categories]
    : ["All", "Popular", "ShowGames", "Favorites", "New", "Slots", "Live"]

  const displayProviders = titles
    ? ["ALL", ...titles]
    : [
      "ALL",
      "EVOLUTION",
      "PRAGMATICPLAYLIVE",
      "EZUGI",
      "TVBET",
      "ALG",
      "VIVOGAMING",
      "NETENT",
      "PLAYTECH",
      "MICROGAMING",
      "QUICKSPIN",
      "YGGDRASIL",
      "PLAYNGO",
      "REDTIGER",
      "BETSOFT",
      "HABANERO",
    ]

  // Flatten infinite scroll data or use fallback games
  const apiGames: (Game | LegacyGame)[] = infiniteGameData?.pages.flatMap(page => page.games) || fallbackGames

  // Display logic: show favorites when toggle is active, otherwise show search results
  const displayGames: (Game | LegacyGame)[] = showFavorites
    ? (favoritesData?.data?.favorites || [])
    : apiGames

  useEffect(() => {
    if (searchOpen && searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }, [searchOpen])

  useEffect(() => {
    // Close dropdown when clicking outside
    function handleClickOutside(event: MouseEvent) {
      if (providerDropdownRef.current && !providerDropdownRef.current.contains(event.target as Node)) {
        setShowProviderDropdown(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  const toggleSearch = () => {
    setSearchOpen(!searchOpen)
  }

  const toggleFavorites = () => {
    setShowFavorites(!showFavorites)
  }

  const toggleProviderDropdown = () => {
    setShowProviderDropdown(!showProviderDropdown)
  }

  const selectProvider = (provider: string) => {
    setSelectedProvider(provider)
    setShowProviderDropdown(false)
  }

  // Desktop version
  if (!isMobile) {
    return (
      <div className="min-h-screen bg-black text-white">
        <div className="container mx-auto px-2 py-4">
          {/* Search and Filter Bar */}
          <div className="bg-[#272729] p-1 mb-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search (Press Enter to search)"
                  value={desktopSearchTerm}
                  onChange={(e) => setDesktopSearchTerm(e.target.value)}
                  onKeyDown={handleDesktopSearchKeyDown}
                  className="w-full h-9 pl-9 pr-3 py-1 rounded-none bg-[#181818] border-none focus:outline-none focus:ring-1 focus:ring-[#F9D501] text-sm"
                />
              </div>

              <div className="relative">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      className="bg-gradient-to-b from-[#4E4E4E] to-[#323232] text-white border-none w-full h-9 justify-between rounded-none text-sm px-3 py-1"
                    >
                      {formatText(activeCategory)}
                      <svg
                        width="12"
                        height="12"
                        viewBox="0 0 15 15"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        className="ml-2 h-3 w-3"
                      >
                        <path
                          d="M4.5 6L7.5 9L10.5 6"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        ></path>
                      </svg>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    className="border-none bg-[#181818] p-0 rounded-none max-h-60 overflow-y-auto dark-scrollbar"
                    style={{ width: "var(--radix-dropdown-menu-trigger-width)" }}
                  >
                    {displayCategories.map((category) => (
                      <DropdownMenuItem
                        key={category}
                        className="text-white bg-[#181818] hover:bg-[#F9D501] hover:text-black focus:bg-[#F9D501] focus:text-black rounded-none h-8 text-sm cursor-pointer"
                        onSelect={() => setActiveCategory(category)}
                      >
                        {formatText(category)}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              <div className="relative">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      className="bg-gradient-to-b from-[#4E4E4E] to-[#323232] text-white border-none w-full h-9 justify-between rounded-none text-sm px-3 py-1"
                    >
                      {formatText(selectedProvider)}
                      <svg
                        width="12"
                        height="12"
                        viewBox="0 0 15 15"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        className="ml-2 h-3 w-3"
                      >
                        <path
                          d="M4.5 6L7.5 9L10.5 6"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        ></path>
                      </svg>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    className="border-none bg-[#181818] p-0 rounded-none max-h-60 overflow-y-auto dark-scrollbar"
                    style={{ width: "var(--radix-dropdown-menu-trigger-width)" }}
                  >
                    {displayProviders.map((provider) => (
                      <DropdownMenuItem
                        key={provider}
                        className="text-white bg-[#181818] hover:bg-[#F9D501] hover:text-black focus:bg-[#F9D501] focus:text-black rounded-none h-8 text-sm cursor-pointer"
                        onSelect={() => selectProvider(provider)}
                      >
                        {formatText(provider)}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              <Button
                variant="outline"
                className={`border-none w-full h-9 rounded-none text-sm px-3 py-1 flex justify-between items-center ${
                  showFavorites
                    ? "bg-[#FCD301] text-black hover:bg-[#E6C001]"
                    : "bg-gradient-to-b from-[#4E4E4E] to-[#323232] hover:bg-gradient-to-b hover:from-[#4E4E4E] hover:to-[#323232] text-white"
                }`}
                onClick={toggleFavorites}
              >
                <span>Favorite</span>
                <Heart className={`h-4 w-4 ${showFavorites ? "fill-black" : ""}`} />
              </Button>
            </div>
          </div>

          {/* Game Grid */}
          {(gamesLoading || (showFavorites && favoritesLoading)) ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {Array.from({ length: 8 }).map((_, index) => (
                <div key={index} className="aspect-[16/9] bg-gray-800 animate-pulse rounded"></div>
              ))}
            </div>
          ) : gamesError ? (
            <div className="text-center py-8">
              <p className="text-red-400 mb-4">Failed to load games</p>
              <p className="text-gray-400">Using fallback games</p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {displayGames.map((game, index) => (
                  <DesktopGameCard key={`${game.id}-${index}`} game={game} />
                ))}
              </div>

              {/* Load more trigger - only show when not displaying favorites */}
              {!showFavorites && hasNextPage && (
                <div ref={loadMoreRef} className="flex justify-center py-8">
                  {isFetchingNextPage ? (
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#FCD301]"></div>
                      <span className="text-gray-400">Loading more games...</span>
                    </div>
                  ) : (
                    <button
                      onClick={() => fetchNextPage()}
                      className="px-6 py-2 bg-[#FCD301] text-black rounded hover:bg-[#E6C001] transition-colors"
                    >
                      Load More Games
                    </button>
                  )}
                </div>
              )}

              {/* Show message when favorites is active but no favorites exist */}
              {showFavorites && displayGames.length === 0 && !favoritesLoading && (
                <div className="text-center py-8">
                  <Heart className="h-12 w-12 text-gray-600 mx-auto mb-4" />
                  <p className="text-gray-400 mb-2">No favorite games yet</p>
                  <p className="text-gray-500 text-sm">Click the heart icon on games to add them to your favorites</p>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    )
  }

  // Mobile version
  return (
    <div className="min-h-screen bg-black text-white w-full overflow-hidden">
      {/* Category Navigation with Search Toggle */}
      <div className="flex w-full">
        {/* Search Toggle */}
        <button onClick={toggleSearch} className="p-3 flex items-center justify-center bg-[#4F4F4F] flex-shrink-0">
          <Search className="h-5 w-5" />
        </button>

        {/* Search Input and Categories - Responsive Layout */}
        <div className="flex-1 flex w-full overflow-hidden">
          {/* Search Input */}
          <div className={`transition-all duration-300 bg-[#4F4F4F] flex-shrink-0 ${searchOpen ? "w-3/4" : "w-0"}`}>
            <input
              ref={searchInputRef}
              type="text"
              placeholder="Search games (min 3 chars)..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full h-full px-3 bg-[#4F4F4F] border-none focus:outline-none text-sm"
            />
          </div>

          {/* Category Navigation */}
          <div
            className={`overflow-x-auto scrollbar-hide transition-all duration-300 ${searchOpen ? "w-1/4" : "w-full"}`}
            ref={categoryScrollRef}
          >
            <div className="flex min-w-max">
              {displayCategories.map((category) => (
                <button
                  key={category}
                  className={`px-6 py-3 text-sm font-medium whitespace-nowrap ${
                    activeCategory === category ? "bg-[#FCD301] text-black" : "bg-[#4F4F4F]"
                  }`}
                  onClick={() => setActiveCategory(category)}
                >
                  {formatText(category)}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Filter Bar */}
      <div className="bg-[#272729] flex items-center w-full">
        <button
          className={`flex items-center justify-between px-3 py-2 ${
            showFavorites ? "bg-[#FCD301] text-black" : "bg-[#E74C3C]"
          } flex-1`}
          onClick={toggleFavorites}
        >
          <Heart className={`h-4 w-4 mr-2 ${showFavorites ? "fill-black" : "fill-white"}`} />
          <span className="text-sm">Show favorites only</span>
        </button>

        <div className="relative flex-1" ref={providerDropdownRef}>
          <button
            className="flex items-center justify-between px-3 py-2 bg-[#272729] w-full"
            onClick={toggleProviderDropdown}
          >
            <span className="text-sm mr-2">{formatText(selectedProvider)}</span>
            <ChevronDown className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Game Grid */}
      {(gamesLoading || (showFavorites && favoritesLoading)) ? (
        <div className="grid grid-cols-2 gap-2 p-2">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="aspect-[16/9] bg-gray-800 animate-pulse rounded"></div>
          ))}
        </div>
      ) : gamesError ? (
        <div className="text-center py-8">
          <p className="text-red-400 mb-4">Failed to load games</p>
          <p className="text-gray-400">Using fallback games</p>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-2 gap-2 p-2">
            {displayGames.map((game, index) => (
              <MobileGameCard key={`${game.id}-${index}`} game={game} />
            ))}
          </div>

          {/* Load more trigger - only show when not displaying favorites */}
          {!showFavorites && hasNextPage && (
            <div ref={loadMoreRef} className="flex justify-center py-4 px-2">
              {isFetchingNextPage ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-[#FCD301]"></div>
                  <span className="text-gray-400 text-sm">Loading more...</span>
                </div>
              ) : (
                <button
                  onClick={() => fetchNextPage()}
                  className="px-4 py-2 bg-[#FCD301] text-black rounded text-sm hover:bg-[#E6C001] transition-colors"
                >
                  Load More Games
                </button>
              )}
            </div>
          )}

          {/* Show message when favorites is active but no favorites exist */}
          {showFavorites && displayGames.length === 0 && !favoritesLoading && (
            <div className="text-center py-8 px-4">
              <Heart className="h-10 w-10 text-gray-600 mx-auto mb-3" />
              <p className="text-gray-400 mb-2 text-sm">No favorite games yet</p>
              <p className="text-gray-500 text-xs">Tap the heart icon on games to add them to your favorites</p>
            </div>
          )}
        </>
      )}

      {/* Full-width Provider Dropdown (Fixed Position) */}
      {showProviderDropdown && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-40" onClick={() => setShowProviderDropdown(false)}>
          <div
            className="absolute left-0 right-0 top-[84px] bg-white z-50 shadow-lg max-h-[60vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            {displayProviders.map((provider) => (
              <div
                key={provider}
                className="flex justify-between items-center px-4 py-3 border-b border-gray-200 last:border-b-0 cursor-pointer"
                onClick={() => selectProvider(provider)}
              >
                <span className="font-medium text-black">{formatText(provider)}</span>
                {selectedProvider === provider && <Check className="h-5 w-5 text-red-600" />}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

